import { attach, createEffect, createEvent, createStore } from 'effector';

import { API2 } from '@dat/api2';
import { sharedTemplateStores } from '../template';

/*Events*/
const getAllCustomTags = createEvent();
const setAssignAttachmentTags = createEvent<{ attachmentId: number; tagIds: number[] }>();
const getDocumentById = createEvent<number>();
const resetDocumentById = createEvent();

/*Effects*/
const getCustomTagsBaseFx = createEffect(API2.myClaimInternal.getCustomTags);
const getCustomTagsFx = attach({
    source: { networkType: sharedTemplateStores.templateSettings.networkType },
    effect: ({ networkType }) => {
        return getCustomTagsBaseFx({ networkType });
    }
});
const getDocumentByIdFx = createEffect(API2.myClaimInternal.getDocument);
const assignAttachmentTagsFx = createEffect(API2.myClaimInternal.assignAttachmentTags);

/*Stores*/
const allCustomTags = createStore<DAT2.Internal.Response.GetCustomTags>([]).on(
    getCustomTagsBaseFx.doneData,
    (state, payload) => payload || state
);

const documentById = createStore<DAT2.Internal.Response.GetDocumentsById | null>(null)
    .on(getDocumentByIdFx.doneData, (state, payload) => payload ?? state)
    .reset(resetDocumentById);

/*Exports*/
export const sharedAttachmentTagEvents = { setAssignAttachmentTags, getDocumentById, getAllCustomTags };
export const sharedAttachmentTagEffects = { getDocumentByIdFx, assignAttachmentTagsFx, getCustomTagsFx };
export const sharedAttachmentTagStores = { documentById, allCustomTags };
