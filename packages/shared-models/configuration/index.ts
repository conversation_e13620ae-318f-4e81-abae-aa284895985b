/*
 * Global customer-configuration which contains settings for different users, templates, claimBuilders, etc.
 * Stores for templates are located in separate folder `template`.
 */
import { combine, createEffect, createEvent, createStore, restore } from 'effector';
import { PartialDeep } from 'type-fest';
import { PartialObjectDeep } from 'type-fest/source/partial-deep';

import { API2 } from '@dat/api2';
import { INITIAL_CUSTOMER_SETTINGS, INITIAL_USER_SETTINGS } from './constants';
import { makeRestoreable } from '@dat/core/utils/effector/makeRestoreable';
import { getParsedArraySafe } from '@dat/api2/utils';
import { filterArrayOfObjectsByExistingKeys } from '@dat/core/utils';
import { filterClaimBuilderForUserRole } from '../contract/utils/filterClaimBuildersForUserRole';
import { sharedLoginEvents } from '../auth/login';

/*
 * Main customer configuration
 * Don't use this store directly - configuration can be invalid because it's fetched from server
 */
const customerConfiguration = createStore<PartialDeep<DAT2.CustomerConfiguration>>({});

/*
 * Settings stores are always preprocessed and valid
 */
const customerSettings = restore(makeRestoreable(INITIAL_CUSTOMER_SETTINGS()));
const userSettings = restore(makeRestoreable(INITIAL_USER_SETTINGS));

//this is used to set userSettings for accounts which don't have configuration but use standalone plugins
const setUserSettingsFormStandaloneComponent = createEvent<PartialObjectDeep<DAT2.CustomerSettings, {}>>();
const userSettingsFormStandaloneComponent = restore(setUserSettingsFormStandaloneComponent, null).reset(
    sharedLoginEvents.loggedOut
);

const templateRoles = createStore<DAT2.Roles | null>(null);

const userRole = createStore<string>('');

const claimBuilders = combine(
    [customerConfiguration, userSettings.availableTemplateIds, templateRoles, userRole],
    ([customerConfiguration, availableTemplateIds, roles, userRole]) => {
        const claimBuildersVisibility = roles?.accessibility?.claimBuildersVisibility;

        const initialBuilders = getParsedArraySafe(customerConfiguration.claimBuilders);

        const filteredClaimBuilders = filterClaimBuilderForUserRole({
            initialBuilders,
            claimBuildersVisibility,
            userRole
        });
        const initialBuildersForUserRole =
            !!userRole && !!filteredClaimBuilders.length ? filteredClaimBuilders : initialBuilders;

        const validBuilders = filterArrayOfObjectsByExistingKeys(initialBuildersForUserRole, [
            'name',
            'templateId'
        ]) as DAT2.ClaimBuilder[];

        if (availableTemplateIds) {
            return validBuilders.filter(({ templateId }) => availableTemplateIds.includes(templateId));
        }

        return validBuilders;
    }
);

const deeplinkScenariosConfiguration = createStore<PartialDeep<DAT2.DeeplinkConfiguration>>({});

const scenarioId = createStore(0);

const showDeductible = combine(
    {
        role: customerSettings.role,
        showDeductibleExpertOnly: userSettings.showDeductibleExpertOnly
    },
    ({ role, showDeductibleExpertOnly }) => !showDeductibleExpertOnly || role === 'EXPERT'
);
const getIncrementalNumberConfigFx = createEffect(API2.bff.customer.getIncrementalNumberPattern);
const incrementalNumberConfig = createStore<null | BFF.Customer.Response.GetIncrementalNumberPattern>(null).on(
    getIncrementalNumberConfigFx.doneData,
    (_, response) => response
);

export const sharedConfigurationStores = {
    customerConfiguration,
    customerSettings, // object
    userSettings, // object
    claimBuilders,
    userRole,
    deeplinkScenariosConfiguration,
    scenarioId,
    showDeductible,
    templateRoles,
    incrementalNumberConfig,
    userSettingsFormStandaloneComponent
};

export const sharedConfigurationEvents = {
    setUserSettingsFormStandaloneComponent
};

export const sharedConfigurationEffects = {
    getIncrementalNumberConfigFx
};

export { type ConfigEditorSchema } from './types';
