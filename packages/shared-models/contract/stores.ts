/*
 * Contract (claim) - is the main entity of all data in DAT.
 *
 * Nested folder-structure in this directory mimics the structure of a contract itself,
 * so if you want to get e.g. `contract.Dossier.Vehicle` you should go to "contract/Dossier/Vehicle/" folder.
 * Most complete spec for contract's structure can be found in SoapUI project (e.g. createOfUpdateContract method)
 *
 * All nested stores containing data from contract should be mapped from main `contract` store.
 * We are using this approach because it allows to update only one `contract` store (usually via `getContract`)
 * without worrying about nested stores updates - they're updated automatically.
 * (!) Mapped stores can't be updated manually (effector will warn you in console if you do so).
 * If you need to update such store, you should update it's parent.
 *
 * (!) Be careful with stores that don't depend on `contract`, because they keep their state
 * throughout the whole lifecycle of the app, unless you manually clear them.
 * Uncleared state can cause significant impact on memory. Example: `allVehiclesImages`.
 */
import { attach, createEffect, createEvent, createStore, restore, sample } from 'effector';
import { uniq } from 'lodash-es';
import deepmerge from 'deepmerge';
import { format } from 'date-fns';

import * as DAT5 from '@wedat/api';
import { API2 } from '@dat/api2';
import { getParsedArraySafe } from '@dat/api2/utils';
import { offlineDb } from '@dat/api2/utils/indexed-db';
import { markAsReadClaim } from '@dat/api2/services/myClaim-internal';
import { extractVinResultFromContract } from '@dat/core/utils/extractVinResultFromContract';
import { throttleEffect } from '@dat/core/utils/effector/throttleEffect';
import { createHistory } from '@dat/api2/services/bff/customer';

import {
    PayloadForCreateContract,
    PayloadForUpdateContract,
    PayloadForUpdateCurrentContract,
    PayloadForCalculateCurrentContract,
    PayloadForCalculateOptimizedCurrentContract,
    HistoryStoreEvent,
    CreateHistoryPayload,
    ActionType
} from './types';
import { calculateContractBaseFx, createOrUpdateContractBaseFx, updateContractBaseFx } from '../baseEffects';
import { mergeEntry } from './utils/mergeEntry';
import { subscribeEffectsToToast } from '../smart-components/Toast';
import { createInitialPayloadForCreateContract } from './utils/createInitialPayloadForCreateContract';
import { sharedTemplateStores } from '../template';
import { sharedUserStores } from '../user';
import { fixContractTypeFields } from './utils/fixContractTypeFields';
import { openAiAdapter } from '@dat/core/utils/DO_AI';

const { templateSettings } = sharedTemplateStores;

const createOrUpdateContractFxCustomMerge = (key: string) => {
    if (key === 'entry') {
        return mergeEntry;
    }
};

const createOrUpdateContractFx = throttleEffect({
    timeout: 3000,
    deepmergeOptions: { customMerge: createOrUpdateContractFxCustomMerge },
    sourceFx: createOrUpdateContractBaseFx
});

const createContractFx = attach({
    source: [
        templateSettings.country,
        templateSettings.currency,
        sharedUserStores.userProfile,
        sharedUserStores.username
    ],
    mapParams: (payload: PayloadForCreateContract, [country, currency, userProfile, username]) =>
        deepmerge(
            payload || {},
            createInitialPayloadForCreateContract({
                country,
                currency,
                userProfile,
                username
            })
        ),
    effect: createOrUpdateContractFx
});

const updateContractFx = attach({
    effect: createOrUpdateContractFx,
    mapParams: (payload: PayloadForUpdateContract) => {
        if (!payload?.contractId) throw new Error('ContractId is empty. Contract update is not possible!');
        return { ...payload, isUpdateContract: true };
    }
});

const getContractFx = createEffect(async (contractId: number | string) => {
    contractId = +contractId;

    if (isNaN(contractId)) throw new Error(`${getContractFx.name}: contractId must be number`);
    if (!navigator.onLine) {
        const contract = await offlineDb.claims.get(+contractId);
        if (!contract) throw new Error(`${getContractFx.name}: bad response`);

        return contract;
    }

    const response = await API2.myClaim.getContract({ contractId });
    const contract = response.return;

    if (!contract) throw new Error(`${getContractFx.name}: bad response`);

    return contract;
});

const markAsReadClaimFx = createEffect(markAsReadClaim);

const getContractStatusFx = createEffect(async (contractId: number | string) => {
    contractId = +contractId;
    if (isNaN(contractId)) throw new Error(`${getContractStatusFx.name}: contractId must be number`);

    const response = await DAT5.MyClaimExternalService.getContractStatus({ contractId });
    const contractStatus = response.responseObj.return;

    if (!contractStatus) throw new Error(`${getContractStatusFx.name}: bad response`);

    return contractStatus;
});

const resetContract = createEvent();

const contractStatusReceived = getContractStatusFx.doneData;
const contractStatus = restore(contractStatusReceived, null).reset(resetContract);
const initialContractDatECode = createStore<string | null>(null);

// Store contains contractId of the current open claim
// We write value even before receiving response to the getContract request
const setCurrentOpenedContractId = createEvent<string | number>();
const currentOpenedContractId = restore(setCurrentOpenedContractId, null).reset(resetContract);

//equal true when we are on the newContract page and trigger createContract effect
const setIsCreatingContract = createEvent();
const isCreatingContract = createStore(false)
    .on(setIsCreatingContract, () => true)
    .reset(resetContract);

const contractReceived = createEvent<DAT2.ContractFromGetContract>();
const setCalculateContractData = createEvent<DAT2.Response.calculateContract>();
const contract = createStore<DAT2.ContractFromGetContract | null>(null)
    .on(contractReceived, (_, data) => fixContractTypeFields(data))
    .on(setCalculateContractData, (contract, calculateContract) =>
        fixContractTypeFields({
            ...contract,
            Dossier: calculateContract.calculationResult?.Dossier || contract?.Dossier
        })
    )
    .reset(resetContract);

initialContractDatECode
    .on(contractReceived, (state, data) => {
        if (!navigator.onLine) {
            return state;
        }

        if (state && state === data?.Dossier?.Vehicle?.DatECode) {
            return state;
        }

        return data?.Dossier?.Vehicle?.DatECode ?? null;
    })
    .reset(resetContract);

const isContractReceived = contract.map(Boolean);
const contractId = createStore<number>(0).reset(resetContract);

const updateInitialContract = createEvent();

const newContractReceived = createEvent<DAT2.ContractFromGetContract>();

sample({
    clock: [contractId.updates, updateInitialContract],
    source: contract,
    filter: Boolean,
    target: newContractReceived
});

const setHistoryStore = createEvent<HistoryStoreEvent>();
const historyStore = restore<CreateHistoryPayload>(setHistoryStore, {});

const createHistoryFx = createEffect((payload: CreateHistoryPayload) =>
    createHistory({
        action: payload?.action as ActionType,
        claimId: payload.claimId,
        username: payload.username as string,
        fullName: `${payload.name} ${payload.surname}`,
        avatar: payload.avatar,
        role: payload.role,
        date: format(Date.now(), 'yyyy-MM-dd'),
        currentStatus: payload.statusName
    })
);

const updateCurrentContractFx = attach({
    source: contractId,
    effect: (contractId, payload: PayloadForUpdateCurrentContract) => {
        if (!navigator.onLine) {
            offlineDb.syncStore.put(
                {
                    contractId,
                    payload,
                    updatedAt: new Date(),
                    type: 'contract'
                },
                `${contractId}_${new Date()}`
            );
        }
        // Condition for contractId === 0, we don't need updateContractFx, when contractId === 0
        if (contractId) {
            return updateContractFx({ contractId, ...payload });
        }
        throw new Error(`contractId wrong`);
    }
});

const updateContractAfterParcellaChangeFx = attach({
    source: {
        pending: createOrUpdateContractBaseFx.pending
    },
    effect: async ({ pending }, payload: PayloadForUpdateContract) => {
        if (pending) {
            setTimeout(() => {
                updateContractAfterParcellaChangeFx(payload);
            }, 300);
        } else {
            await updateContractBaseFx(payload);
            await getContractFx(payload.contractId);
            contractEvents.updateInitialContract();
        }
    }
});

const updateContractAfterParcellaChangeThrottleFx = throttleEffect({
    timeout: 3000,
    deepmergeOptions: { customMerge: createOrUpdateContractFxCustomMerge },
    sourceFx: updateContractAfterParcellaChangeFx
});

const initialContract = restore(newContractReceived, null).reset(resetContract);
const templateId = contract.map(contract => contract?.complexTemplateData?.templateId || null); // in most cases you should use `templateId` from `template` stores

const complexTemplateData = contract.map(contract => getParsedArraySafe(contract?.complexTemplateData?.field));
const customTemplateData = contract.map(contract => getParsedArraySafe(contract?.customTemplateData?.entry));
const networkType = contract.map(contract => contract?.networkType || null);

const isContractDisabled = createStore(false);

const setIsGettingContractAfterUpdateEnabled = createEvent<boolean>();
const isGettingContractAfterUpdateEnabled = restore(setIsGettingContractAfterUpdateEnabled, true);

const calculateContractFx = createEffect(calculateContractBaseFx);

const calculateCurrentContractFx = attach({
    source: contractId,
    effect: (contractId, payload: PayloadForCalculateCurrentContract) => calculateContractFx({ contractId, ...payload })
});

const calculateOptimizedFx = createEffect(API2.myClaimInternal.calculateOptimized);
const calculateOptimizedCurrentContractFx = attach({
    source: contractId,
    effect: (contractId, payload: PayloadForCalculateOptimizedCurrentContract) =>
        calculateOptimizedFx({ claimId: contractId, ...payload })
});
const updateContractAfterOptimizedCalculationFx = attach({
    source: contractId,
    effect: async contractId => {
        const newContract = await getContractFx(contractId);
        contractEvents.newContractReceived(newContract);
    }
});

const isLoadingCalculateContract = calculateCurrentContractFx.pending;

// this one is for when you meed a loader in claim
const calculateCurrentContractWithLoaderFx = createEffect(calculateCurrentContractFx);

const resetContractInVSM = createEvent();
const contractInVSM = createStore<DAT2.ContractFromGetContract | null>(null).reset(resetContractInVSM);

const vinResult = contract.map(contract => (contract && extractVinResultFromContract(contract)) || null);

const vinEquipmentResult = vinResult.map(vinResult =>
    vinResult
        ? (uniq(
              vinResult.vinRequestResultEquipment?.vinResultEquipment
                  .filter(item => !!item.datNumber)
                  .map(item => item.datNumber)
          ) as string[])
        : []
);

//from memo field
const lastSuccessfullyRequestedVin = vinResult.map(vinResult => vinResult?.vin || '');
const lastSuccessfullyRequestedLicencePlate = vinResult.map(
    vinResult => vinResult?.registrationNumberRequestResult || ''
);

//from contract
const vinDataFromContract = contract.map(contract => ({
    VinResult: contract?.Dossier?.Vehicle?.VINResult,
    TokenOfVinResult: contract?.Dossier?.Vehicle?.TokenOfVinResult
}));

const isAppraisalContract = templateSettings.restriction.map(restriction => restriction === 'APPRAISAL');

subscribeEffectsToToast({ getContractFx });

const saveCroatianTranslationsFx = createEffect(
    async ({
        contractId,
        materialPositions,
        lacquerPositions,
        labourPositions,
        discountPositions,
        extensionPositions
    }: {
        contractId: number;
        materialPositions: DAT2.MaterialPosition[];
        lacquerPositions: DAT2.MaterialPosition[];
        labourPositions: DAT2.LabourPosition[];
        discountPositions: DAT2.DiscountPosition[];
        extensionPositions: DAT2.ExtensionPosition[];
    }) => {
        // Helper to chunk an array into batches of 10
        function chunkArray<T>(arr: T[], size: number): T[][] {
            const res: T[][] = [];
            for (let i = 0; i < arr.length; i += size) {
                res.push(arr.slice(i, i + size));
            }
            return res;
        }

        async function translateDescriptions(descriptions: string[]): Promise<string[]> {
            if (!descriptions.length) return [];
            const chunks = chunkArray(descriptions, 10);
            const responses = await Promise.all(chunks.map(chunk => openAiAdapter({ message: chunk.join('|') })));
            // Merge all translations in order
            return responses.flatMap(res => {
                if (typeof res === 'object' && res !== null && 'openAiResponse' in res) {
                    return res.openAiResponse ? res.openAiResponse.split('|') : [''];
                }
                return [''];
            });
        }

        const materialDescriptions = materialPositions
            .map(item => item.Description)
            .filter((d): d is string => typeof d === 'string');
        const lacquerDescriptions = lacquerPositions
            .map(item => item.Description)
            .filter((d): d is string => typeof d === 'string');
        const labourDescriptions = labourPositions
            .map(item => item.Description)
            .filter((d): d is string => typeof d === 'string');
        const discountDescriptions = discountPositions
            .map(item => item.Description)
            .filter((d): d is string => typeof d === 'string');
        const extensionDescriptions = extensionPositions
            .map(item => item.Description)
            .filter((d): d is string => typeof d === 'string');

        const [
            materialTranslationsArray,
            lacquerTranslationsArray,
            labourTranslationsArray,
            discountTranslationsArray,
            extensionTranslationsArray
        ] = await Promise.all([
            translateDescriptions(materialDescriptions),
            translateDescriptions(lacquerDescriptions),
            translateDescriptions(labourDescriptions),
            translateDescriptions(discountDescriptions),
            translateDescriptions(extensionDescriptions)
        ]);

        const dataObject = {
            materialPositions: materialPositions.map((item, idx) => ({
                ...item,
                translatedDescription: materialTranslationsArray[idx]
            })),
            lacquerPositions: lacquerPositions.map((item, idx) => ({
                ...item,
                translatedDescription: lacquerTranslationsArray[idx]
            })),
            labourPositions: labourPositions.map((item, idx) => ({
                ...item,
                translatedDescription: labourTranslationsArray[idx]
            })),
            discountPositions: discountPositions.map((item, idx) => ({
                ...item,
                translatedDescription: discountTranslationsArray[idx]
            })),
            extensionPositions: extensionPositions.map((item, idx) => ({
                ...item,
                translatedDescription: extensionTranslationsArray[idx]
            }))
        };

        updateContractBaseFx({
            contractId,
            templateData: {
                entry: [
                    {
                        key: 'repairPositionsTranslation',
                        value: {
                            _text: JSON.stringify(dataObject),
                            type: 'xs:string'
                        }
                    }
                ]
            }
        });
    }
);

/*** Export ***/
export const contractEvents = {
    contractReceived,
    newContractReceived,
    setIsGettingContractAfterUpdateEnabled,
    updateInitialContract,
    resetContract,
    resetContractInVSM,
    setCalculateContractData,
    setCurrentOpenedContractId,
    setIsCreatingContract,
    setHistoryStore
};

export const contractEffects = {
    createOrUpdateContractFx,
    createContractFx,
    updateContractFx,
    getContractFx,
    updateCurrentContractFx,
    calculateContractFx,
    calculateCurrentContractFx,
    calculateOptimizedFx,
    calculateOptimizedCurrentContractFx,
    updateContractAfterOptimizedCalculationFx,
    getContractStatusFx,
    calculateCurrentContractWithLoaderFx,
    markAsReadClaimFx,
    createHistoryFx,
    updateContractAfterParcellaChangeThrottleFx,
    saveCroatianTranslationsFx
};

export const contractStores = {
    contract,
    vinResult,
    vinEquipmentResult,
    contractId,
    currentOpenedContractId,
    isContractReceived,
    initialContractDatECode,
    initialContract,
    templateId,
    complexTemplateData,
    customTemplateData,
    networkType,
    isContractDisabled,
    isGettingContractAfterUpdateEnabled,
    contractStatus,
    contractInVSM,
    isLoadingCalculateContract,
    isCreatingContract,
    historyStore,
    lastSuccessfullyRequestedVin,
    lastSuccessfullyRequestedLicencePlate,
    isAppraisalContract,
    vinDataFromContract
};
