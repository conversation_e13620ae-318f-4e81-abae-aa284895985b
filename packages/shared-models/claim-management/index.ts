import { combine, restore, createEvent, createEffect } from 'effector';
import { createGate } from 'effector-react';

import { API2 } from '@dat/api2';
import { SetPropertiesRecursive } from '@dat/core/types/utility';

import { sharedTemplateStores } from '../template';
import { DEFAULT_CONFIGURATION } from './contants/DEFAULT_CONFIGURATION';
import { setActiveCalculation } from '@dat/api2/services/myClaim-internal';

// quick explanation here. Effector .map is stupid. when initial value is undefined, it will never run the function inside.
// this is why I used combine instead 🥲
const configuration = combine([sharedTemplateStores.productsConfiguration], ([conf]) => {
    return conf?.['claim-management'] || DEFAULT_CONFIGURATION();
});

export const ClaimPageGate = createGate<SetPropertiesRecursive<{ contractId: string | 'new' }, string>>();

const isNewContractPageOpened = restore(
    ClaimPageGate.open.map(({ contractId }) => contractId === 'new'),
    false
);

const setIsClaimManagementStandalone = createEvent<boolean>();
const isClaimManagementStandalone = restore(setIsClaimManagementStandalone, false);

const setDatInternalConfig = createEvent<DAT2.Internal.Response.LoadModuleConfig>();

const getDatInternalConfigFx = createEffect(() => API2.myClaimInternal.loadModuleConfig({ moduleId: '/inbox.jsp' }));
const datInternalConfig = restore(getDatInternalConfigFx.doneData, null).on(
    setDatInternalConfig,
    (_, payload) => payload
);

const setActiveCalculationBaseFx = createEffect(setActiveCalculation);

export const sharedClaimManagementStores = {
    configuration,
    isClaimManagementStandalone,
    datInternalConfig,
    isNewContractPageOpened
};
export const sharedClaimManagementEvents = {
    setIsClaimManagementStandalone,
    setDatInternalConfig
};
export const sharedClaimManagementEffects = {
    getDatInternalConfigFx,
    setActiveCalculationBaseFx
};
