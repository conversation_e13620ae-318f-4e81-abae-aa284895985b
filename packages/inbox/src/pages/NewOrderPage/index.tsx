import { FC, ChangeEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { useUnit } from 'effector-react';
import { useHistory } from 'react-router-dom';

import { sharedConfigurationStores } from '@dat/shared-models/configuration';
import { sharedTemplateEvents } from '@dat/shared-models/template';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sharedUserStores } from '@dat/shared-models/user';
import { sizes } from '@wedat/ui-kit/mediaQueries';

import polygons from '../../assets/images/polygons.png';
import { useTheme } from 'styled-components';
import { AiClaim } from '../../components/Modals/AiClaim';
import {
    ButtonWrapper,
    CloseButton,
    Content,
    DrawerButton,
    ImportLabel,
    ImportLaptop,
    StyledText,
    TemplateWrapper,
    UploadImage,
    UploadInput,
    UploadLabel,
    Wrapper
} from './styles';
import { orderEffects, orderEvents } from '../../stores/order';
import { pluginStores } from '../../stores/plugin';
import { importIniEffects } from '../../stores/importIni';
import { modalsEvents } from '../../stores/ui/modals';
import { svdImportEffects } from '../../stores/euroSv/svdImport';
import { ArrowUpIcon } from '@wedat/ui-kit';
import { CardLayoutDefault } from './CardLayoutDefault';
import { CardLayoutIni } from './CardLayoutIni';

interface Props {
    inModal?: boolean;
}

export const NewOrderPage: FC<Props> = ({ inModal }) => {
    const { t } = useTranslation();
    const {
        colors: { dustBlue }
    } = useTheme();
    const history = useHistory();
    const isLaptop = useMedia(sizes.laptop);
    const isMobile = useMedia(sizes.phoneBig);

    const claimBuilders = useUnit(sharedConfigurationStores.claimBuilders);
    const pluginOptions = useUnit(pluginStores.pluginOptions);
    const importIni = useUnit(pluginStores.pluginOptions)?.settings?.importIni;
    const showSvdImport = useUnit(pluginStores.pluginOptions)?.settings?.showSvdImport;
    const documentId = useUnit(pluginStores.pluginOptions)?.settings?.importIniFolder;
    const customerNumber = useUnit(sharedUserStores.customerNumber);

    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
        importIniEffects.handleFileInputClickFx({ customerNumber, event, templateField: importIni, documentId });
    };

    const handleClick = async ({ layoutType, ...rest }: DAT2.ClaimBuilder) => {
        if (layoutType === 'guided') {
            sharedTemplateEvents.setTemplate(rest?.templateId);
            history.push(`/guided`);
            return;
        }

        pluginOptions?.resetVSMStores?.();
        orderEvents.createOrderWithType({
            ...rest,
            layoutType: layoutType || 'standard'
        });
    };

    const handleCancel = () => {
        if (isMobile) {
            history.goBack();
        } else {
            modalsEvents.toggleIsNewOrderDrawerOpen(false);
        }
    };

    const handleSvdImport = (event: ChangeEvent<HTMLInputElement>) => {
        const files = event?.target?.files;
        if (!files) return;

        svdImportEffects.handleUploadSvdZipsFx({ files });
        handleCancel();
    };

    const handleIniImportAlternative = async (event: ChangeEvent<HTMLInputElement>, templateId: number) => {
        event?.stopPropagation();
        const file = event?.target?.files?.[0];
        if (!file) return;

        orderEffects.handleUploadIniFileFx({ file, templateId });
        handleCancel();
    };

    return (
        <Wrapper>
            <Content>
                {!inModal && (
                    <>
                        <CloseButton onClick={() => history.goBack()}>
                            <ArrowUpIcon />
                        </CloseButton>

                        <AiClaim />
                    </>
                )}
                {importIni && (
                    <>
                        {isLaptop ? (
                            <ImportLaptop>
                                <UploadImage src={polygons} alt="upload image" />
                                <ImportLabel>
                                    <UploadInput hidden type="file" accept=".txt,.ini" onChange={handleChange} />
                                    <StyledText>{t('inbox:newOrder.importClaim')}</StyledText>
                                </ImportLabel>
                            </ImportLaptop>
                        ) : (
                            <UploadLabel>
                                <UploadInput hidden type="file" accept=".txt,.ini" onChange={handleChange} />
                                <UploadImage src={polygons} alt="upload image" />
                                <StyledText color={dustBlue['600']}>{t('inbox:newOrder.importClaim')}</StyledText>
                            </UploadLabel>
                        )}
                    </>
                )}
                {showSvdImport && (
                    <>
                        {isLaptop ? (
                            <ImportLaptop>
                                <UploadImage src={polygons} alt="upload image" />
                                <ImportLabel>
                                    <UploadInput
                                        hidden
                                        type="file"
                                        multiple
                                        accept=".zip, .rar, .7z, .tar, .gz"
                                        onChange={handleSvdImport}
                                    />
                                    <StyledText>{t('inbox:newOrder.svdImport')}</StyledText>
                                </ImportLabel>
                            </ImportLaptop>
                        ) : (
                            <UploadLabel>
                                <UploadInput
                                    hidden
                                    type="file"
                                    multiple
                                    accept=".zip, .rar, .7z, .tar, .gz"
                                    onChange={handleSvdImport}
                                />
                                <UploadImage src={polygons} alt="upload image" />
                                <StyledText color={dustBlue['600']}>{t('inbox:newOrder.svdImport')}</StyledText>
                            </UploadLabel>
                        )}
                    </>
                )}

                <StyledText font="defaultBold">{t('inbox:newOrder.templateTitle')}</StyledText>
                <TemplateWrapper>
                    {claimBuilders.map(builder =>
                        builder.layoutType === 'importIniAlternative' ? (
                            <CardLayoutIni builder={builder} onIniImportAlternative={handleIniImportAlternative} />
                        ) : (
                            <CardLayoutDefault
                                builder={builder}
                                onCardClick={handleClick}
                                onIniImportAlternative={handleIniImportAlternative}
                            />
                        )
                    )}
                </TemplateWrapper>
            </Content>

            <ButtonWrapper>
                <DrawerButton typeStyle={{ type: 'square', color: 'blue' }} onClick={handleCancel}>
                    {t('inbox:cancel')}
                </DrawerButton>
            </ButtonWrapper>
        </Wrapper>
    );
};
