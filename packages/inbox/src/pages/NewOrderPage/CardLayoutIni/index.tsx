import { FC, ChangeEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components';
import polygons from '../../../assets/images/polygons.png';
import { ImportIniCard, StyledText, TemplateText, UploadImage, UploadInput } from '../styles';

interface Props {
    builder: DAT2.ClaimBuilder;
    onIniImportAlternative: (event: ChangeEvent<HTMLInputElement>, templateId: number) => void;
}

export const CardLayoutIni: FC<Props> = ({ builder, onIniImportAlternative }) => {
    const {
        colors: { dustBlue }
    } = useTheme();
    const { t } = useTranslation();
    return (
        <ImportIniCard key={builder.name} data-cy={`${builder.templateId}-${builder.layoutType}`}>
            <UploadInput
                hidden
                type="file"
                accept=".txt, .svd, .ini"
                onChange={e => onIniImportAlternative(e, builder.templateId)}
            />
            <UploadImage height={66} src={polygons} alt={t('upload image')} />

            <TemplateText>
                <StyledText font="footnoteBold">{builder.name}</StyledText>
                <StyledText font="font12" color={dustBlue['600']}>
                    {t(builder.layoutType, { default: builder.layoutType })}
                </StyledText>
            </TemplateText>
        </ImportIniCard>
    );
};
