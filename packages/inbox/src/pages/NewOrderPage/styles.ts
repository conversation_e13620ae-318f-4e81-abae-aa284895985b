import styled from 'styled-components/macro';
import { Button as ButtonDefault } from '@wedat/ui-kit/components/Button';
import { makeCustomScrollBarForDrawer, media } from '@wedat/ui-kit/mediaQueries';
import { Text } from '@wedat/ui-kit';

export const Wrapper = styled.div`
    position: relative;
    height: 100%;

    ${media.phone`
        max-width: 100%;
    `}
`;

export const Content = styled.div`
    overflow: auto;
    padding: 0 16px;
    height: calc(100% - 74px);

    ${makeCustomScrollBarForDrawer()}

    ${media.phoneBig`
        padding-bottom: 74px;
    `}
`;

export const StyledText = styled(Text)`
    display: block;
`;

export const DefaultImage = styled.img`
    width: 66px;
    height: 66px;
`;

export const UploadLabel = styled.label`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 24px;
    margin-top: 12px;
    margin-bottom: 16px;
    transition: all 0.1s linear;
    cursor: pointer;

    border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['50']}`};

    &:hover {
        border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['100']}`};
    }
`;

export const ImportIniInCard = styled.label`
    width: 92px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: auto;
    padding: 6px;
    transition: border 0.1s linear;
    cursor: pointer;

    border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['50']}`};

    &:hover {
        border: ${({ theme }) => `2px dashed ${theme.colors.dustBlue['100']}`};
    }
`;

export const ImportIniCard = styled.label`
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;

    border: ${({ theme }) => `1px solid ${theme.colors.gray_10}`};

    @media (hover: hover) {
        &:hover {
            background-color: ${({ theme }) => theme.colors.gray['100']};
        }
    }

    &:active {
        background-color: ${({ theme }) => theme.colors.gray['200']};
    }
`;

export const UploadInput = styled.input``;

export const UploadImage = styled.img``;

export const ImportLaptop = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 24px;
    margin-bottom: 40px;
`;

export const ImportLabel = styled.label`
    border-radius: 8px;
    padding: 8px 24px;
    min-width: 160px;
    cursor: pointer;

    ${({ theme }) => theme.typography.defaultText}
    border: ${({ theme }) => `1px solid ${theme.colors.dustBlue['400']}`};
    color: ${({ theme }) => theme.colors.dustBlue['900']};
`;

export const TemplateWrapper = styled.div`
    display: flex;
    flex-direction: column;
    gap: 12px;
    flex: 1;
    margin-top: 12px;
    padding-bottom: 12px;
`;

export const Template = styled.div`
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;

    border: ${({ theme }) => `1px solid ${theme.colors.gray_10}`};

    @media (hover: hover) {
        &:hover {
            background-color: ${({ theme }) => theme.colors.gray['100']};
        }
    }

    &:active {
        background-color: ${({ theme }) => theme.colors.gray['200']};
    }
`;

export const TemplateText = styled.div``;

export const ButtonWrapper = styled.div`
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px 24px;
    height: 74px;

    background-color: ${({ theme }) => theme.colors.white};
    border-top: ${({ theme }) => `1px solid ${theme.colors.dustBlue['100']}`};

    ${media.phoneBig`
        position: fixed;
    `}
`;

export const DrawerButton = styled(ButtonDefault)`
    max-width: 148px;
    text-transform: capitalize;

    ${({ theme }) => theme.typography.note};

    ${media.phoneBig`
        max-width: initial;
    `};
`;

export const CloseButton = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    background-color: ${({ theme }) => theme.colors.white};
    border: 1px solid ${({ theme }) => theme.colors.dustBlue['300']};
    border-radius: 8px;
    margin-top: 12px;
    cursor: pointer;
    color: ${({ theme }) => theme.colors.dustBlue['900']};

    &:focus-visible,
    &:focus,
    &:hover,
    &:active {
        border-color: ${({ theme }) => theme.colors.dustBlue['400']};
        color: ${({ theme }) => theme.colors.dustBlue['900']};
    }

    transform: rotate(90deg);
`;
