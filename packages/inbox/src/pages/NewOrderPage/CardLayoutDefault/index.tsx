import { FC, ChangeEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from 'styled-components';
import defaultImage from '../../../assets/images/default.png';
import polygons from '../../../assets/images/polygons.png';
import { DefaultImage, ImportIniInCard, StyledText, Template, TemplateText, UploadImage, UploadInput } from '../styles';

interface Props {
    builder: DAT2.ClaimBuilder;
    onCardClick: (builder: DAT2.ClaimBuilder) => void;
    onIniImportAlternative: (event: ChangeEvent<HTMLInputElement>, templateId: number) => void;
}

export const CardLayoutDefault: FC<Props> = ({ builder, onIniImportAlternative, onCardClick }) => {
    const {
        colors: { dustBlue }
    } = useTheme();
    const { t } = useTranslation();

    return (
        <Template
            key={builder.name}
            data-cy={`${builder.templateId}-${builder.layoutType}`}
            onClick={() => onCardClick(builder)}
        >
            <DefaultImage src={builder.templateIconUrl || defaultImage} alt="default tempalte image" />
            <TemplateText>
                <StyledText font="footnoteBold">{builder.name}</StyledText>
                <StyledText font="font12" color={dustBlue['600']}>
                    {t(builder.layoutType, { default: builder.layoutType })}
                </StyledText>
            </TemplateText>
            {!!builder.showImportIni && (
                <ImportIniInCard onClick={e => e.stopPropagation()}>
                    <UploadInput
                        hidden
                        type="file"
                        accept=".txt, .svd, .ini"
                        onChange={e => onIniImportAlternative(e, builder.templateId)}
                    />
                    <UploadImage src={polygons} alt="upload image" />
                </ImportIniInCard>
            )}
        </Template>
    );
};
