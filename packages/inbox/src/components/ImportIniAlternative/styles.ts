import { media } from '@wedat/ui-kit/mediaQueries';
import styled, { css } from 'styled-components/macro';
import { StyledText } from '../Header/styles';

export const ImportIniText = styled(StyledText)<{ $templateColor?: string; $templateColorActive?: string }>`
    ${({ $templateColor }) =>
        $templateColor &&
        css`
            color: ${$templateColor};
        `}
    cursor: pointer;
    transition:
        color 150ms,
        opacity 150ms;

    @media (hover: hover) {
        &:hover {
            color: ${({ theme, $templateColorActive }) =>
                $templateColorActive ? $templateColorActive : theme.colors.primary};
            opacity: ${({ $templateColorActive }) => $templateColorActive && '0.5'};
        }
    }
`;
export const Input = styled.input``;

export const Label = styled.label<{ disabled?: boolean }>`
    pointer-events: ${({ disabled }) => (disabled ? 'none' : 'unset')};
    ${({ disabled }) => disabled && 'opacity: 0.5;'};

    ${media.tablet`
        display: none;
    `}
`;
