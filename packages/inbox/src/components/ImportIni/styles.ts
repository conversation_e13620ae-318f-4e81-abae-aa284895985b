import { media } from '@wedat/ui-kit/mediaQueries';
import styled, { css } from 'styled-components/macro';
import { Item, StyledText } from '../Header/styles';

export const ImportIniContainer = styled(Item)`
    ${media.tablet`
        display: none;
    `}

    ${({ disabled }) =>
        disabled &&
        `
             opacity: 0.5; 
        `}
`;
export const ImportIniText = styled(StyledText)<{ $templateColor?: string }>`
    ${({ $templateColor }) =>
        $templateColor &&
        css`
            color: ${$templateColor};
        `}
    cursor: pointer;
    transition:
        color 150ms,
        opacity 150ms;
    @media (hover: hover) {
        &:hover {
            color: ${({ theme, $templateColor }) => ($templateColor ? $templateColor : theme.colors.primary)};
            opacity: ${({ $templateColor }) => $templateColor && '0.5'};
        }
    }
`;
export const Input = styled.input``;

export const Label = styled.label``;
