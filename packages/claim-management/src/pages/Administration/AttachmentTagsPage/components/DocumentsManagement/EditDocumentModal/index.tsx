import { useUnit } from 'effector-react';
import { Form, Formik } from 'formik';
import { useTranslation } from 'react-i18next';

import { attachmentTagsEvents, attachmentTagsStores } from '../../../../../../stores/attachmentTags';
import { useMedia } from '@dat/core/hooks/useMedia';
import { sizes } from '@wedat/ui-kit/mediaQueries';
import { Modal } from '@wedat/kit';

import { ModalContent } from './ModalContent';
import { TRANSLATION_PATH } from '../../../constants';
import { INITIAL_VALUES } from './constants';

export const EditDocumentModal = () => {
    const { t } = useTranslation();
    const isMobile = useMedia(sizes.phone);
    const isOpenedEditDocumentDrawer = useUnit(attachmentTagsStores.isOpenedEditDocumentDrawer);
    const selectedDocument = useUnit(attachmentTagsStores.selectedDocument);

    const handleSubmit = (values: DAT2.Internal.ItemListDocuments) => {
        attachmentTagsEvents.saveDocument({
            data: { ...values, manual_publish: false }
        });
        attachmentTagsEvents.setIsOpenedEditDocumentDrawer(false);
    };

    return (
        <Modal
            unsetOverflow
            bodyHeight="auto"
            header={t(`${TRANSLATION_PATH}.editDocumentDrawer.title`)}
            onHide={() => attachmentTagsEvents.setIsOpenedEditDocumentDrawer(false)}
            visible={isOpenedEditDocumentDrawer}
            bodyNoPadding
            fullWidth
            maxWidth={isMobile ? '100%' : '400px'}
        >
            <Formik
                key={selectedDocument?.id}
                onSubmit={handleSubmit}
                initialValues={selectedDocument || INITIAL_VALUES}
            >
                <Form>
                    <ModalContent />
                </Form>
            </Formik>
        </Modal>
    );
};
