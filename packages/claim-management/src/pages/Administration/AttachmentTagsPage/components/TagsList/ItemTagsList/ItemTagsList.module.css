@import url('@wedat/kit/src/styles/variables.css');

.card {
    position: relative;
    display: flex;
    width: 100%;
    height: fit-content;
    flex-direction: column;
    background-color: var(--color-white);
    border-bottom: 2px solid var(--color-gray-100);
    margin-bottom: 8px;
    border-radius: 4px;

    @media (hover: hover) {
        &:hover {
            background-color: var(--color-dust-blue-50);
        }
    }

    @media (--phone-big) {
        height: 100%;
        margin-bottom: 4px;
        background-color: var(--color-white);
        border-bottom: 2px solid var(--color-gray-100);
        overflow: hidden;
    }
}

.card_active {
    background-color: var(--color-dust-blue-50);
}

.baseInfoWrapper {
    width: 100%;
    height: 100%;
    display: flex;
    padding-left: 10px;
    padding-right: 10px;
    align-items: center;
    justify-content: space-between;
    color: var(--color-dust-blue-600);

    @media (--phone-big) {
        padding-left: 8px;
        padding-right: 8px;
    }
}

.columnWrapper {
    padding: 10px 4px;
    display: flex;
    flex-direction: column;
    word-break: break-word;
    font-size: 12px;
    line-height: 16px;
    font: var(--font-footnote);
    flex: 1 1 100%;


    &:first-of-type {
        padding-left: 8px;
    }
    &:last-of-type {
        padding-right: 8px;
    }

    @media (--phone-big) {
        &:first-of-type {
            padding-left: 0;
        }
        &:last-of-type {
            padding-right: 0;
        }
    }
}

.columnWrapperHorizontal_center {
    align-items: center;
}

.columnWrapperHorizontal_end {
    align-items: flex-end;
}

.columnWrapperVertical_center {
    justify-content: center;
}

.columnWrapperVertical_end {
    justify-content: flex-end;
}

.columnHasColumWidth {
    flex: 0 0 var(--itemColumnWidth);
}

.columnWrapperHorizontal_center {
    align-items: center;
}

.columnWrapperHorizontal_end {
    align-items: flex-end;
}

.columnWrapperVertical_center {
    justify-content: center;
}

.columnWrapperVertical_end {
    justify-content: flex-end;
}

.columnWrapper_row {
    flex-direction: row;
}

.columnWrapper_bold {
    font-weight: 700;
    margin-bottom: 6px;
}

.iconWrapper {
    width: 24px;
    height: 24px;
    display: flex;
    align-self: center;
    justify-self: flex-end;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.iconWrapper_opened {
    color: var(--color-dust-blue-700);
}