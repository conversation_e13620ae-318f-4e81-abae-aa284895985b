import { combine, sample } from 'effector';

import { attachmentsEffects, attachmentsStores } from '../attachments';
import { fileEditorEffects, fileEditorEvents, fileEditorStores } from '.';
import { contractStores } from '@dat/shared-models/contract';
import { sharedGalleryStores } from '@dat/shared-models/gallery';

import { reorderAttachments } from '../../utils/reorderAttachments';

const { attachments, activeGroupId } = attachmentsStores;
const { changeAttachmentsOrderFx, listAttachmentsOfContractFx } = attachmentsEffects;

const { contractId } = contractStores;

const { groupedAttachments } = sharedGalleryStores;

const { editedFileName, reorderTargetIndex } = fileEditorStores;
const { setEditedFileName } = fileEditorEvents;
const { saveEditedFileFx, restoreFileFx } = fileEditorEffects;

const combinedSource = combine({
    attachments,
    editedFileName,
    reorderTargetIndex,
    groupedAttachments,
    activeGroupId,
    claimId: contractId
});

sample({
    clock: [saveEditedFileFx.doneData, restoreFileFx.doneData],
    source: { contractId },
    target: listAttachmentsOfContractFx
});

sample({
    clock: listAttachmentsOfContractFx.doneData,
    source: combinedSource,
    filter: ({ attachments, editedFileName }) =>
        !!editedFileName && attachments.some(({ fileName }) => fileName === editedFileName),
    fn: ({ groupedAttachments, activeGroupId, reorderTargetIndex, attachments, claimId, editedFileName }) => {
        const foundImageId = attachments.find(({ fileName }) => fileName === editedFileName)?.id || 0;
        const reorderedAttachmentsIds = reorderAttachments({
            attachments: groupedAttachments[activeGroupId],
            id: foundImageId,
            index: reorderTargetIndex
        }).map(({ id }) => id ?? 0);

        return {
            claimId,
            folderId: activeGroupId,
            attachmentsIds: reorderedAttachmentsIds.reverse()
        };
    },
    target: changeAttachmentsOrderFx
});

sample({
    clock: changeAttachmentsOrderFx.doneData,
    source: {
        contractId
    },
    target: listAttachmentsOfContractFx
});

sample({
    clock: changeAttachmentsOrderFx.doneData,
    fn: () => null,
    target: setEditedFileName.prepend(() => ({ fileName: null, index: 0 }))
});
