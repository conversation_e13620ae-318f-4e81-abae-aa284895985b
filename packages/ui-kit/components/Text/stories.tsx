import { Meta, <PERSON> } from '@storybook/react';
import { Text } from './index';
import { type TextProps } from './types';

const Template: Story<TextProps> = (args: TextProps) => (
    <Text {...args}>The quick brown fox jumps over the lazy dog</Text>
);

export const TextPrimary = Template.bind({});

export const TextRegular = Template.bind({});

TextRegular.args = {
    fontWeight: 'normal',
    fontSize: '16px'
};

export const TextBold = Template.bind({});

TextBold.args = {
    fontWeight: 'bold',
    fontSize: '16px'
};

export default {
    title: 'Design System/UI Kit/Text',
    component: Text
} as Meta;
