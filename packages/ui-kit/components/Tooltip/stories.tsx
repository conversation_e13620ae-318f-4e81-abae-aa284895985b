import { Meta, <PERSON> } from '@storybook/react';
import { Tooltip } from './index';
import { TooltipProps } from './types';

const Template: Story<TooltipProps> = (args: TooltipProps) => (
    <Tooltip {...args}>
        <span>Some component</span>
    </Tooltip>
);

export const TooltipTemplate = Template.bind({});
TooltipTemplate.args = {
    text: 'some text...',
    placement: 'auto',
    width: undefined
};

export default {
    title: 'Design System/UI Kit/Tooltip',
    component: Tooltip
} as Meta;
