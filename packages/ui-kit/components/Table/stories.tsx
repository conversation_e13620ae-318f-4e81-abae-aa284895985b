import { <PERSON>a, <PERSON> } from '@storybook/react';
import { Table } from './index';

const columns = [
    {
        Header: 'Description',
        accessor: 'col1'
    },
    {
        Header: 'DAT',
        accessor: 'col2'
    },
    {
        Header: 'Manually',
        accessor: 'col3'
    }
];

const data = [
    {
        col1: 'Listed original price',
        col2: 'Some text',
        col3: 'Some text'
    },
    {
        col1: 'Reference value',
        col2: 'Some text',
        col3: 'Some text'
    },
    {
        col1: 'Correction by first registration',
        col2: 'Some text',
        col3: 'Some text'
    },
    {
        col1: (
            <>
                {'Correction by mileage details'}
                <br />
                {'Reference driving route'}&nbsp;
                {'Some tag'}
                <br />
                {'Mileage'}&nbsp;
                {'Some tag'}
                <br />
                {'Mileage difference'}&nbsp;
                {'Some tag'}
            </>
        ),
        col2: 'Some tag',
        col3: 'Some tag'
    },
    {
        col1: 'Base value determined by DAT',
        col2: 'Some tag',
        col3: 'Some text'
    }
];

const Template: Story = () => <Table data={data} columns={columns} mobileHeaders={true} />;

export const TextPrimary = Template.bind({});

export default {
    title: 'Design System/UI Kit/Table',
    component: Table
} as Meta;
