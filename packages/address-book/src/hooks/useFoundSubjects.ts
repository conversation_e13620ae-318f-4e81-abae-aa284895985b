import { useMemo } from 'react';
import { useUnit } from 'effector-react';
import { omit } from 'lodash-es';
import { sharedUserStores } from '@dat/shared-models/user';
import { sharedInboxStores } from '@dat/shared-models/inbox';
import { instancesStores } from '../stores/instances';
import { HISTORY_INSPECTION, HISTORY_INSPECTION_CONTACT, REPAIRER } from '../constants/names';
import { useSelectedModel } from './useSelectedModel';

export const useFoundSubjects = () => {
    const { stores } = useSelectedModel();

    const { currentSubjects, subjectType, memoFieldName, withUserNameField, editorType } = useUnit(stores.instance);

    const searchValue = useUnit(stores.searchValue);
    const instances = useUnit(instancesStores.instances);

    const isInbox = useUnit(sharedInboxStores.inboxOpened);
    const userName = useUnit(sharedUserStores.username);
    //for contactPerson subject list  we need to filter and show the subjects corresponding to the repairer code and userName
    const contactSubjectDependency = useMemo(() => {
        let contactSubjectDependencyValue = '';
        if (subjectType === HISTORY_INSPECTION_CONTACT && instances) {
            const allSubjects = Object.values(instances);

            const foundDependency = !isInbox
                ? allSubjects.find(subject => subject.subjectType === HISTORY_INSPECTION)
                : allSubjects.find(subject => subject.subjectType === REPAIRER);

            contactSubjectDependencyValue = (foundDependency?.displayedSubject?.code as string) ?? '';
        }
        return contactSubjectDependencyValue;
    }, [instances, subjectType, isInbox]);

    // to search with multiply words in form values
    const foundSubjects = useMemo(() => {
        const searchWords = searchValue
            .toUpperCase()
            .split(' ')
            .filter(word => word !== '');
        return currentSubjects?.filter(subject =>
            searchWords.every(word =>
                Object.values(omit(subject, ['_id'])).some(value => String(value).toUpperCase().includes(word))
            )
        );
    }, [currentSubjects, searchValue]);

    const filteredByWorkshopId = foundSubjects.filter(subject => {
        const matchesWorkshop = subject.workshopId === contactSubjectDependency;
        const matchesUserName = subject.userName === userName;
        const hasNoName = !subject.name;

        return (matchesWorkshop || hasNoName) && matchesUserName;
    });
    //filter editor subject list based on fieldName
    const listWithEditors = useMemo(() => {
        return editorType
            ? foundSubjects
                  .filter(sub => !sub?.name || (!!sub?.fieldName && sub.fieldName === memoFieldName))
                  .filter(sub => !withUserNameField || !sub.userName || sub.userName === userName)
            : foundSubjects;
    }, [foundSubjects, memoFieldName, userName, withUserNameField, editorType]);

    const filteredFoundSubjects =
        subjectType === HISTORY_INSPECTION_CONTACT && contactSubjectDependency ? filteredByWorkshopId : listWithEditors;

    return filteredFoundSubjects;
};
