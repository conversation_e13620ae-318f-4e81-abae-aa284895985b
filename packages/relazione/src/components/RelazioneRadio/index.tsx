import { FC, useMemo } from 'react';
import { FieldInputProps } from 'formik';
import { useTranslation } from 'react-i18next';

import { RadioGroupField, RadioValue } from '@dat/smart-components/FormikFields';

import { relazioneFormEvents } from '../../stores/form';
import { useRelazioneFormik } from '../../hooks';
import { RadioVariants, getRadioItemsByVariant } from '../../constants';
import { RelzioneRadioStyled, RelzioneRadioLabel } from './styles';
import { RelazioneFormikKeys } from '../../types';

interface Props {
    name: RelazioneFormikKeys;
    label: string;
    variant: RadioVariants;
}

export const RelazioneRadio: FC<Props> = ({ label, variant, ...rest }) => {
    const { values } = useRelazioneFormik();
    const { t } = useTranslation();

    const saveField2 = relazioneFormEvents.saveField;

    const handleChange = ({ value }: FieldInputProps<RadioValue>) => {
        saveField2({ values, value: value as string, key: rest.name });
    };

    const options = useMemo(() => getRadioItemsByVariant(t, rest.name)[variant], [rest.name, t, variant]);

    return (
        <RelzioneRadioStyled>
            <RelzioneRadioLabel>{label}</RelzioneRadioLabel>
            <RadioGroupField isWithoutWrap onUserChange={handleChange} withoutGap radioItems={options} {...rest} />
        </RelzioneRadioStyled>
    );
};
