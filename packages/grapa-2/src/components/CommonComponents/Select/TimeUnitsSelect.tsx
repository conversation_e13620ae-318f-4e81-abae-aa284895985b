import { Select, SelectOption } from '@wedat/ui-kit';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import './timeUnitsSelect.css';

interface Props {
    value: string;
    timeUnitsPerHour?: number;
    onChange: (option: SelectOption | null) => void;
}

export const TimeUnitsSelect = memo<Props>(({ value, timeUnitsPerHour, onChange }) => {
    const { t } = useTranslation();

    return (
        <div className="weDat-grapa-timeUnitsSelect">
            <Select
                name="timeUnits"
                inputSize="verySmall"
                value={value}
                label={`1${t('ManualRepairPositionForm.HoursShort', 'h')} = ${timeUnitsPerHour || ''}${t(
                    'ManualRepairPositionForm.TimeUnitsShort',
                    'TU'
                )}`}
                fixedLabel
                defaultWhiteSpaceOption
                onChange={onChange}
                options={[
                    { value: 'hours', label: t('ManualRepairPositionForm.Hours') },
                    { value: 'units', label: t('ManualRepairPositionForm.TimeUnits') }
                ]}
            />
        </div>
    );
});
