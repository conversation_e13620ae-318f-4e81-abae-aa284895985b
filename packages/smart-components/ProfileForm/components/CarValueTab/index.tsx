import { FC } from 'react';
import { useTranslation } from 'react-i18next';
import { useFormikContext } from 'formik';

import { FormProps, FormUserProfile, SelectOption } from '../../types';

import { Container, FieldsSubtitle, FormStyled } from '../form/styles';
import { useTheme } from 'styled-components';
import { NumberInputField } from '@wedat/ui-kit/Formik';
import { BID_CURRENCY_LIST, BOOLEAN_FIELDS, FLOAT_FIELDS, NUMBER_FIELDS, STRING_FIELDS } from './constants';
import { FormBlock } from '../DynamicForm/FormBlock';
import { selectCountries } from '../../constants';

import { CheckboxField, InputField, SelectField } from '../../../FormikFields';

export const CarValueTab: FC<FormProps> = ({ forms = [], style }) => {
    const { t } = useTranslation();
    const {
        colors: { gray_300 }
    } = useTheme();
    const { errors } = useFormikContext<FormUserProfile>();

    const selectCurrency = (): SelectOption[] =>
        BID_CURRENCY_LIST.map((currency, index) => ({
            key: index,
            value: currency,
            label: currency
        }));

    return (
        <Container style={style}>
            <FieldsSubtitle asTag="h5" fontSize="16px" textTransform="capitalize" color={gray_300}>
                {t('auth:carValue.form.titles.carValues')}
            </FieldsSubtitle>
            <FormStyled>
                {NUMBER_FIELDS.map(field => (
                    <InputField
                        key={field}
                        name={field}
                        label={t(`auth:carValue.form.${field}`)}
                        helperText={errors[field]}
                        inputId="smart-components__profile-form__carValueTab"
                    />
                ))}
            </FormStyled>
            <FormStyled>
                {STRING_FIELDS.map(field => (
                    <SelectField
                        options={field === 'country' ? selectCountries(t) : selectCurrency()}
                        name={field}
                        key={field}
                        label={t(`auth:carValue.form.${field}`)}
                        inputId="smart-components__profile-form__carValueTab"
                        aria-label={t(`auth:carValue.form.${field}`)}
                    />
                ))}
            </FormStyled>
            <FormStyled>
                {FLOAT_FIELDS.map(field => (
                    <NumberInputField key={field} name={field} label={t(`auth:carValue.form.${field}`)} />
                ))}
            </FormStyled>
            <FormStyled>
                {BOOLEAN_FIELDS.map(field => (
                    <CheckboxField
                        type="checkbox"
                        key={field}
                        name={field}
                        content={t(`auth:carValue.form.${field}`)}
                        aria-label={t(`auth:carValue.form.${field}`)}
                        inputId="smart-components__profile-form__carValueTab"
                    />
                ))}
            </FormStyled>
            {...forms.map(form => <FormBlock form={form} key={form.title} />)}
        </Container>
    );
};
