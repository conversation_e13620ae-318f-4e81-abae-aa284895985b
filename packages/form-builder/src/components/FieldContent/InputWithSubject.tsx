import { FC } from 'react';

import { InputField } from '@dat/smart-components/FormikFields';

import { AddressBookField } from './AddressBookField';
import { DEFAULT_SUBJECT_FOR_EDITABLE_TEXT_FOR_STRING } from '../../constants';
import { Container, AddressBookContainer, IconContainer } from './styles';

interface Props {
    name: string;
    color?: string;
    label?: string;
    multiselect?: boolean;
    withUserNameField?: boolean;
}

export const InputWithSubject: FC<Props> = props => {
    const { multiselect, withUserNameField, ...clearProps } = props;

    return (
        <Container>
            <InputField
                {...clearProps}
                id={`form-builder_inputWithSubject_${props.name}`}
                borderLeftColor={clearProps.color}
            />

            <IconContainer small>
                <AddressBookContainer small>
                    <AddressBookField
                        field={{
                            ...DEFAULT_SUBJECT_FOR_EDITABLE_TEXT_FOR_STRING,
                            label: props.label,
                            memoFieldName: props.name,
                            id: props.name,
                            multiselect,
                            withU<PERSON><PERSON><PERSON><PERSON><PERSON>,
                            editorType: DEFAULT_SUBJECT_FOR_EDITABLE_TEXT_FOR_STRING.id
                        }}
                    />
                </AddressBookContainer>
            </IconContainer>
        </Container>
    );
};
