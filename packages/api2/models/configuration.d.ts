declare namespace DAT2 {
    import { type ReactNode } from 'react';
    import { type TFunctionResult, type Resource } from 'i18next';
    import { type Tag } from '@dat/core/types';

    type PartialDeep<T> = import('type-fest').PartialDeep<T>;
    type TemplateId = number;
    type UserTheme = {
        /** Enter one of the options:
         * "true" The header style becomes blue and the tabs label yellow.
         * "false" The header style is white and tabs label are blue ( <PERSON><PERSON><PERSON> ) */
        dark?: boolean;
        /** Enter one of the options:
         * "true" The logo is separated by a diagonal line, the home icon in the stamp is recolored and moved to the white area (the burger icon and label are not added). */
        HIG?: boolean;
        /** Enter one of the options:
         * "true" The logo on top left shows SilverDAT logo.
         * "false" The logo top left shows the weDAT logo. */
        silverLogo?: boolean;
        /** Enter one of the options:
         * "true" The logo on top left shows logo.
         * "false" The logo top left shows the weDAT logo. */
        datLogo?: boolean;
        customFont?: {
            light: string;
            regular: string;
            bold: string;
            black: string;
        };
    };

    /* Customer */
    interface CustomerConfiguration {
        /** Here you configure the main settings of the customer number */
        settings: CustomerSettings;

        /** Here you define what the users can do in weDAT. Always set at least "default" or one username */
        users: {
            /** This will be the default configuration, and will be overridden by customer specific configuration */
            default: UserConfiguration;
            [username: string]: PartialDeep<UserConfiguration>;
        };

        /** Main template configuration object */
        templates: {
            /** Configure the default template settings and products, and will be overridden by template specific configuration */
            default: TemplateConfiguration;
            [templateId: TemplateId]: PartialDeep<TemplateConfiguration>;
        };

        /** Add the available templates that the user can create. */
        claimBuilders?: ClaimBuilder[];

        country?: string;
    }

    type ClaimBuildersVisibility = Record<'AI Claim' | 'Guided Claim' | 'Standard Claim' | 'Manufacturer', string[]>;
    type HeaderBlocks =
        | 'testClaimPlugin'
        | 'vhc'
        | 'vehicle-selection-2'
        | 'grapa'
        | 'grapa-2'
        | 'calculation'
        | 'valuation'
        | 'fast-track'
        | 'fast-track-2'
        | 'ai-gallery-2'
        | 'inbox'
        | 'scheduler'
        | 'darva'
        | 'invoice'
        | 'scheduler-menu'
        | 'notification';

    type SideBarBlocks =
        | 'vehicle-selection'
        | 'equipment'
        | 'technical-data'
        | 'assign-partner'
        | 'printout'
        | 'gallery'
        | 'labour-rates'
        | 'mail'
        | 'create-appointment'
        | 'create-expiry-claim'
        | 'scheduler'
        | 'status'
        | 'damage-selector'
        | 'total-price'
        | 'wallet'
        | 'fuel-level'
        | 'vehicle-history-drawer'
        | 'success-modal'
        | 'vehicle-class-modal'
        | 'download'
        | 'posta-pronta'
        | 'chat'
        | 'digital-signature'
        | 'vin-input'
        | 'number-plate-input'
        | 'kilometer-input'
        | 'tyres-input'
        | 'vehicle-image';

    type TableBlocks =
        | 'carOwner'
        | 'referenceNumber'
        | 'lossNumber'
        | 'String_insurance_name'
        | 'String_expert_name'
        | 'String_repairer_name'
        | 'String_repairer_zip'
        | 'String_damageDate'
        | 'String_auditionDate'
        | 'opened'
        | 'Date_expiryDate'
        | 'lastEntry'
        | 'String_event_type'
        | 'Date_event_approval_date'
        | 'totalPrice'
        | 'address'
        | 'registrationNumber'
        | 'vehicle'
        | 'mileageRead'
        | 'vin'
        | 'statusName'
        | 'String_priority'
        | 'String_wDComponents_name'
        | 'String_taskName'
        | 'image'
        | 'String_responsible_name'
        | 'String_createdByName';

    interface HideBlocksConfiguration {
        header: HeaderBlocks[];
        sidebar: SideBarBlocks[];
        tableColumns: TableBlocks[];
    }

    type tabVisibility = Record<string, HideBlocksConfiguration>;

    type PrintoutReportsConfig = {
        toFilter?: string[];
        toAdd?: DAT2.PrintoutReport[];
    };

    type ParcellaTabAccessibility = {
        parcellaExpertTabVisibility: string[];
        parcellaCollaboratorTabVisibility: string[];
        parcellaExpertReadOnly: string[];
        parcellaCollaboratorReadOnly: string[];
        parcellaAdministrationVisibility: string[];
    };

    type InboxFoldersVisibilityBasedRoles = {
        roles: string[];
        folders: string[];
    };

    interface Roles {
        /** here you define the name of the groups and which usernames belong to this groups */
        role?: Record<string, Record<string, string[]>>[];
        adminNames?: string[];
        /** Here you can assign the groups created in role to each different accessibility function */
        accessibility?: {
            /** Shows only specific templates in the claim builder */
            claimBuildersVisibility?: ClaimBuildersVisibility;
            /** Disable the ability to create new claims */
            notEnableToCreateClaim?: string[];
            /** Disable the administration rights */
            notAllowedToAdministration?: string[];
            /** Revoke ability to modify labourRates */
            labourRatesReadOnly?: string[];
            /** Revoke ability to modify vehicle selection */
            VSMReadOnly?: string[];
            /** Revoke ability to modify Grapa */
            grapaReadOnly?: string[];
            /** evoke ability to modify fastTrack */
            fastTrackReadOnly?: string[];
            /** Revoke ability to change calculation */
            calculationReadOnly?: string[];
            /** Revoke ability to change liquidazione */
            liquidazioneReadOnly?: string[];
            /** Modify the statuses that are available */
            statusTransitionsVisibility?: Record<string, number[]>;
            /** Show only specific tabs */
            tabVisibility?: tabVisibility;
            /** Disable Parcella */
            parcellaTabAccessibility: ParcellaTabAccessibility;
            /** Hide the preview user tab */
            hidePreviewUserTab?: string[];
            /** Disable to preview of photos */
            disablePhotoPreview?: string[];
            /** Change which printouts are visible */
            printoutReports?: {
                [role: string]: PrintoutReportsConfig;
            };
            isVisibleParcella?: string[];
            isHiddenInvoiceInInbox?: string[];
            isHiddenParcellaInInbox?: string[];
            isHiddenParcellaInCalculation?: string[];
            isHiddenLiquidazioneInCalculation?: string[];
            isVisibleProductivityInInbox?: string[];
            assignClaim?: string[];

            /** Show filtered folders based the user(s) role(s) */
            inboxFoldersVisibilityBasedRoles?: InboxFoldersVisibilityBasedRole;
            /** Show filtered folders for default users */
            inboxFoldersVisibilityDefault?: string[];
        };
    }

    interface CustomerSettings {
        /** Enter the role of the customer number */
        role: CustomerRole;
        adminNames?: string[];
        /** Enter either of the two options for video calling: "youCit" to use the youCit endpoint. "viperPro" to use the viperPro endpoint */
        videoCallType?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        bffUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        apiUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        aniaUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        prUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        viperProUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        youCitApiUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        youCitUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        euroSvUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        datgroupUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        mioCodiceFiscaleUrl?: string;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER. Works only in TST env */
        showBugReporter?: boolean;
        /** Set to true if you want the OCR prompt to open when you open a claim */
        OCRwithoutTrigger?: boolean;
        /** WARNING : DON'T TOUCH IF YOU ARE NO DEVELOPER */
        apsUrl?: string;
    }

    /* User */
    interface UserConfiguration {
        /** Welcome to the NERD side. Inside "settings" you will be able to set up your weDAT account */
        settings: UserSettings;
    }

    type ProvidersName = 'GRYP3D' | 'PARTSLINK';

    interface UserSettings {
        /** Enter the locale of your country. Ex. format it-IT */
        locale: Locale;
        /** Enter all the available template Ids.
         * In case you have more than one then separate them with a comma ",".
         * Don't forget to add the default templateId also.
         * The template ID is located in the URL on myClaim->administration->templates. */
        availableTemplateIds?: TemplateId[] | null;
        /** Enter the default template ID.
         * The template ID is located in the URL on myClaim->administration->templates. */
        defaultTemplateId?: TemplateId | null;
        /** Enter one of the options:
         *  "true" Enables the welcome popup when logging in ( Default ).
         *  "false" Disables the welcome popup when logging in */
        welcomePopup?: boolean | null;
        /** Enter one of the options:
         *  "true" Shows the Profile Management after the WelcomePopup at the very first login ( Default ).
         *  "false" Hides the Profile Management after the WelcomePopup at the very first login */
        profileManagement?: boolean | null;
        /** Here you can customize the UI theme of weDAT */
        theme?: UserTheme | null;
        /** Here you can define groups for the users of this customer number.
         * Using the groups created you can define what each group can and cannot do */
        roles?: Roles;
        /** Enter the folder id that you want the youCit photos to be saved to */
        youCitFolderId?: number;
        /** Enter the default credentials for youCit login. This is not needed if you use the credentials of profile side for each user. */
        youCitCredentials?: DAT2.YouCitCredentials;
        /** Enter the address group ( owner/repairer/expert ) of which you want to automatically grab the phone and email from. */
        youCitInheritSubject?: string;
        /** Enter one of the options:
         *  "true" Opens a side drawer when selecting damage in the damage selector.
         *  "false" Only selects the position when you select position in the damage selector ( Default ) */
        advancedPOI?: boolean;
        damageSelectorHidenBlocks?: Array<'intensity' | 'impactPoint' | 'zone' | 'angle'>;
        /** Enter one of the options:
         *  "true" Shows tab of euroTax Austria in inbox.
         *  "false" Hide tab of euroTax in inbox ( Default ). */
        showEuroSv?: boolean;
        /** Enter the id of the folder for the euroTax Austria Files, without pdf extension. */
        euroSvFilesFolderId?: number;
        /** Enter the id of the folder for the euroTax Austria Images. */
        euroSvImagesFolderId?: number;
        /** Enter the id of the folder for the euroTax Austria Pdf. */
        euroSvPdfFolderId?: number;
        /** Enter the credentials for the euroTax Austria. */
        euroSvCredentials?: DAT2.EuroSvCredentials;
        /** Enter one of the options:
         *  "true" Allows user to upload photos in the gallery using the + button.
         *  "false" Disallows user to upload photos in the gallery. ( Default ). */
        manualUpload?: boolean;
        /** Enter the id of the folder that the photos uploaded in grapa will be stored to. */
        partsImagesFolderId?: number;
        vinCoverageShow?: boolean;
        /** Enter one of the options:
         *  "true" Shows vehicle history with a side drawer.
         *  "false" Hides vehicle history with a side drawer ( Default ). */
        showVehicleHistory?: boolean;
        /** Enter one of the options:
         *  "true" Shows Deeplink button.
         *  "false" Hides Deeplink button ( Default ). */
        showDeeplink?: boolean;
        invoiceTemplateId?: number;
        /** Enter one of the options:
         *  "true" In Calculation
         *  "false" Disables Parcella ( Default ). */
        parcellaActive?: boolean;
        /** Enter one of the options:
         *  "true" Hides the deductible from the calculation price in not EXPERT role.
         *  "false" Shows the deductible from the calculation price ( Default ). */
        showDeductibleExpertOnly?: boolean;
        stdMailServiceActive?: boolean;
        expertDefaultId?: string;
        /** Enter one of the options:
         *  "true" Shows tab of euroTax in inbox.
         *  "false" Hide tab of euroTax in inbox ( Default ). */
        showParcellaInbox?: boolean;
        showStandaloneConfigurationTab?: boolean;
        openMyClaimShow?: boolean;
        showSmartRepairAdministration?: boolean;
        defaultTemplateIdForRoles?: number;
        showHailDamages?: boolean;
        signatureLogo?: string;
        /** Enable the part-ordering feature in Grapa/Calculation and account config */
        canOrderParts?:
            | boolean
            | {
                  availableProviders?: ProvidersName[];
                  enableDemoEnv?: boolean;
              };
        showHailDamagesAdministration?: boolean;
        isPossibleShareHailDamagesConfig?: boolean;
        showActivityManager?: boolean;
        showAttachmentTagsAdministration?: boolean;
        isVisibleAttachmentTagsInGallery?: boolean;
        isShowStarInFavoritePicture?: boolean;
        profileCustomItems?: ProfileCustomItem[];
    }

    interface ProfileCustomItem {
        name: string;
        url?: string;
        icon?: string;
    }

    /* Template */
    interface TemplateConfiguration {
        /** Available settings to Configure */
        settings: TemplateSettings;
        products?: ProductsConfiguration;
    }

    interface StatusConfiguration {
        icons?: { [key: string]: string };
        types?: { [key: string]: string[] };
    }

    type customHeaderOrderItem = {
        headerText: string;
        order: number;
    };

    type PaletteFromTemplateType =
        | {
              colorBg?: string | undefined;
              colorText?: string | undefined;
              colorActive?: string | undefined;
              companyLogo?: string | undefined;
              companyLogoHeight?: string | undefined;
              companyLogoAlignLeft?: boolean | undefined;
              companyLogoBorder?: number | undefined;
          }
        | undefined
        | null;

    interface TemplateSettings {
        /** Enter the network of the template.
         * The template network is located in myClaim->administration->templates. */
        networkType: NetworkType;
        /** Enter the type of the template */
        contractType: ContractType;
        /** Enter the country code of your database.
         * The country code is located in license manager->entering the customer number->searching->select the installation product with ASP and press the editor button.
         * Then select the SilverDAT myClaim product and on the bottom you can see the country code in the column "DATA". */
        country: CountryCode;
        datCountryIndicator?: CountryCode | null;
        /** Enter the currency of the country
         * @minimum 3
         * @maximum 3 */
        currency: string;
        restriction: Restriction;
        /** Enter the status that the template will have on creation */
        initialStatus?: string | null;
        /** Enter the status/es on which the user will not be able to enter the claim */
        disabledStatuses?: string[];
        /** Enter the status/es that the viper will be available on */
        activeViperStatuses?: string[];
        /** Enter URLs of JS file */
        externalJS?: string[] | null;
        /** Enter URLs of CSS file */
        externalCSS?: string[] | null;
        /** Enter the status/es that will not be changeable */
        readOnlyStatuses?: string[];
        /** Enter the status/es that the user will be kicked out of claim. */
        pushMeOutStatuses?: string[];
        /** Enter the status in which the claim will be readOnly */
        activeStatusForReadOnlyClaim?: string[];
        /** Enter the icons for the status/es. */
        status?: StatusConfiguration | null;
        /** Enter the status/es where parcella is disabled */
        parcellaDisableStatuses?: string[];
        /** can be configured like 'Belgium' or 'PriceOpelChevrolet', */
        additionalPriceListId?: string | null;
        checkboxFields?: CheckboxFields;
        /** Shows the basic Valuation modal */
        showBasicValuation?: boolean;
        /** Shows the Residual Value */
        showResidualValue?: boolean;
        /** Shows the history of calculations */
        showRestoreCalculation?: boolean;
        showRestoreCalculationType?: boolean;
        showRestoreCalculationRole?: boolean;
        /** Enter extra locales for this template
         *  like 'D_D' or 'RUS_RUS' or 'A_D' */
        additionalLocales?: string[];
        roles?: Roles;
        /** Enable the restoration of a historic calculation */
        restoreCalculationType: string;
        customHeaderOrder?: customHeaderOrderItem[];
        claimColorPalette?: PaletteFromTemplateType | null;
        isPredefinedPositionEditable?: boolean;
        imageAnalysisServiceActive?: boolean;
        useOrchestrator?: boolean;
        hiddenEquipmentIds?: number[];
        /** Statuses that trigger parcella calculation */
        parcellaCalculationEnabledStatuses?: string[];
        triggerOnlyOnStatusChange?: boolean;
    }

    type UnknownProductsConfiguration = {
        [K in Exclude<Plugins.PluginName, keyof KnownProductsConfiguration>]?: unknown;
    };

    type LabourRatesConfiguration = {
        areCheckboxesAllowedWithDisabledInputs?: boolean;
        createNewRate?: boolean;
        labourRatesStep?: boolean;
        allLabourRatesMandatory?: boolean;
        /** Default: 'IT', 'FR', 'GR', 'ES', 'CN', 'E' */
        countriesForHideAzt?: string[];
        readOnly?: boolean;
        isChineseViewEnabled?: boolean;
        overrideWithoutValuesOfAdditionallyRequiredPositions?: boolean;
        displayLongText?: boolean;
        commentLacquerTypeLocale?: boolean;
        networkOwnerCredentials?: {
            customerNumber: number;
            user: string;
        };
        showAztLacquerCondition?: boolean;
        showAztAdditions?: boolean;
        showManufacturerLacquerCondition?: boolean;
        showEuroLacquerCondition?: boolean;
        initSpecialRateForPrincipal?: boolean; //sets different claim rate for hailDamages (subClaim) also

        /** Rental costRate settings */
        rentalCostRateActive?: boolean;
        rentalCostRateField?: string;
        rentalCostRateFieldValues?: (string | number)[];
        /**  */
    };

    // Plugins
    type CalculationConfiguration = {
        areDiscountsHidden?: boolean;
        damageMapping?: boolean;
        editGlobalAmount?: boolean;
        inlineGlobalAmount?: boolean;
        expandAllLinesOnInitialRender?: boolean;
        hideLacquerLevel?: boolean;
        hideLacquerMaterialColumn?: boolean;
        hideLevel?: boolean;
        hideTotalValueCalculated?: boolean;
        isManualAdditionDisabled?: boolean;
        isMicroCalculationEnabled?: boolean;
        readOnly?: boolean;
        showPostEditingCalculation?: boolean;
        showPrice?: boolean;
        showSummary?: boolean;
        showWorkType?: boolean;
        showDvn?: boolean;
        spoCountry?: string;
        spoEnabled?: boolean;
        spoGenericEnabled?: boolean;
        summaryNew?: boolean;
        postEditingFilters?: boolean;
        tableFieldsDisabled?: boolean;
        isReadOnly?: boolean;
        showShortManualPositionForm?: boolean;
        shouldPreventCalculate?: boolean;
        aiTranslationForCroatiaEnabled?: boolean;
        customTags?: Tag[];
        showAggregateTabs?: boolean;
    };

    type VehicleSelectionConfiguration = {
        /** link to a PDF showing all compatible vehicle with DAT data */
        vehicleListURL?: string;
        settings?: {
            /** @default MANUFACTURER_SPECIFIC */
            paintMethod?: DAT.PaintMethod;
            /** @default true */
            repairIncomplete?: boolean;
            /** @default true */
            vehicleImages?: boolean;
            /** @default true */
            firstRegistrationFilter?: boolean;
            /** @default false */
            VINQueryAutomatic?: boolean;
            /** @default false */
            NumberPlateSearchAutomatic?: boolean;
            /** @default false */
            allowPlateSearchForClaimHistory?: boolean;
            /** @default false */
            freeTextSearchAutomatic?: boolean;
            /** @default true */
            DATECodeDisplayedOnModel?: boolean;
            /** @default false */
            AZMainModel?: boolean;
            possibleToUseRecoverClaim?: boolean;
            restriction?: DAT2.Restriction;
            country?: DAT2.CountryCode;
            locale?: DAT2.Locale;
            existingPlateBehavior?: 'recoverData' | 'newVINRequest';
            plateMandatory?: boolean;
            vinMandatory?: boolean;
            vinSearchAllowed?: boolean;
            plateSearchAllowed?: boolean;
            hideVSMParts?: string[];
            vsmReadOnlyAfterVin?: boolean;
            /* previewMode used for readOnly display of the VSM form without performing additional requests,
            i.e. displaying a snapshot of the current contract data */
            previewMode?: boolean;
            vehicleTypes?: string[];
            plateOcrFolderId?: number;
            ocrPlateProvider?: string;
            vinProvider?: string;
            vinFolderId?: number;
            ocrWithoutPlateQuery?: boolean;
            ocrSVG?: string;
            ocrVinSVG?: string;
            showParallelHook?: boolean;
            plateInput?: string;
            vinInput?: string;
            kilometersMandatory?: boolean;
            firstRegistrationMandatory?: boolean;
            showContainer?: boolean;
            showPaintType?: boolean;
            italianPlateProvider?: 'std' | 'alternative';
            isAdvancedEquipment?: boolean;
            mileageOcr?: boolean;
            mileageOcrFolderId?: number;
            disallowSecondVinSearch?: boolean;
            disallowSecondVinSearchForAccount?: boolean;
            isVinOCRVisible?: boolean;
            disableCheckForEquipments?: boolean;
            disableNotifyToastForVin?: boolean;
            isSearchIconVisibleForNumberPlate?: boolean;
            isSearchIconVisibleForVin?: boolean;
            vehicleDescription?: string; // description from contract templateData
            spainPlateProvider?: 'datiberica';
            spainVinProvider?: 'datiberica';
        };
    };
    type EventItem = {
        initialStatus?: string;
        icon?: string;
    };
    type ItalianCalculationConfiguration = {
        optimizationService?: string;
        automaticOptimization?: boolean;
        ASPISBodyshopID?: string;
        ratesReadOnly?: boolean;
        orderingPositions?: boolean;
        copyPositionsToLinkedClaim?: {
            fieldName: string;
            enabled: boolean;
        };
    };

    type BidStatusDetail = {
        statusName: string;
        statusId: number;
    };

    type CustomFooterEntry = {
        description: string;
        iconUrl?: string;
        tooltipText?: string;
    };

    type ClaimManagementConfiguration = {
        name?: string | number;
        hiddenStatuses?: number[];
        ruleForVehicleSelection?: number; //for ApplyRuleSet after vehicle selection
        ruleForVinQuery?: number; //for ApplyRuleSet after vehicle selection if vin is queried
        ruleForRecovery?: number; //for ApplyRuleSet after vehicle selection if behavior is recovery
        customFooter?: CustomFooterEntry[];
        /** cf. SelectOptionDataScheme */
        statusTransition?: DAT2.Plugins.ClaimManagement.StatusTransition[];
        trackUserStatusActivity?: boolean;
        notFilterCurrentStatus?: boolean;
        /** for the Bid claims Status changes */
        bidStatusTransition?: {
            expiredStatus?: BidStatusDetail;
            inProcessStatus?: BidStatusDetail;
        };

        subjectsToUpdateById?: string[];
        isShownMoveToNextStatusButton?: boolean;

        exportZipGeneric?: {
            attachmentsFolderId?: number[];
            printoutId?: number[];
            svdExport?: boolean;
            downloadZipImmediately?: boolean;
            requiredFileCounts?: Record<string, number>;
            checkZipReportsByNames?: boolean; // by predefined export reports names in user profile
            messageForFailedZip?: string;
        };
        chatBot?: {
            isActive: boolean;
            groupId?: number;
            // TODO: check it in next MR photoWidget delete any
            photoWidget?: Record<string, any>;
            tips: {
                action: string;
                title: string;
            }[];
        };
        chatLog?: { attachmentFolderId?: number };
        exportZipEuroSv?: boolean;
        distances?: {
            initAddress?: string;
            finalAddress?: string;
            distanceKM?: string;
        };
        isFooterWithLabels?: boolean;
        /** cf HideBlocksConfiguration */
        hideBlocks?: DAT2.HideBlocksConfiguration;
        /** cf HideBlocksConfiguration */
        showBlocks?: DAT2.Plugins.ClaimManagement.ShowBlocksConfiguration;
        footerLabels?: Array<{
            pluginName: string;
            label: string;
        }>;
        claimColorPalette?: {
            colorBg?: string;
            colorText?: string;
            colorActive?: string;
            companyLogo?: string;
        };
        administration?: DAT2.Plugins.ClaimManagement.Administration;
        sendBid?: {
            printoutId?: string;
        };
        displayGross?: boolean;
        tyresPercentVisibility?: boolean;
        tyresPercentCustomField?: {
            id: string;
            options: Array<{
                value: string | number;
                key?: string | number;
                disabled?: boolean;
                label: string | ReactNode | TFunctionResult;
            }>;
            initValue?: string | number | boolean;
        };
        /** @default true */
        isSparePartsCostsShown?: boolean;
        notAllowCreateNewClaim?: boolean;
        /** href to website */
        technicalSupport?: string;
        /** href to the FAQ */
        howToUseWeDAT?: string;
        /** cf DataScheme formBuilder */
        form?: DAT2.Plugins.FormBuilder.DataScheme;
        /** cf DataScheme formBuilder */
        calculationForm?: DAT2.Plugins.FormBuilder.DataScheme;
        /** cf DataScheme formBuilder */
        quietanza?: DAT2.Plugins.FormBuilder.DataScheme;
        /** cf DataScheme formBuilder */
        parcella?: DAT2.Plugins.FormBuilder.DataScheme;
        /** cf DataScheme formBuilder */
        liquidazione?: DAT2.Plugins.FormBuilder.DataScheme;
        /** for fleetView */
        checkIn?: DAT2.Plugins.FormBuilder.DataScheme;
        fleetView?: boolean;
        /** used for fleetView input validation */
        restrictionForLicensePlate?: DAT2.CountryCode;
        fleetViewSettings?: {
            fleetViewEventClaimCount?: number;
            availableEventList?: Record<string, EventItem>;
            fleetViewTabsHeaders: TabHeader[];
            checkInEventsMandatoryStatus?: number[];
            availableStatusForEventDeletion?: number;
            eventListRuleId?: number;
        };
        basicValuation?: {
            red: BasicValuationConfItem[];
            orange: BasicValuationConfItem[];
        };
        isHiddenLiquidazioneInCalculation?: boolean;
        incrementalNumberSave?: string;
        isCalledSpireContractMBLI?: boolean;
        isCalledVSMAfterSpireContract?: boolean;
    };
    type ProfileConfiguration = {
        showBankAccount?: boolean;
        showCarValueTab?: boolean;
        showApsTab?: boolean;
        hideExportZipTab?: boolean;
        carValueRequiredIds?: string[];
        /** defined in @shared-models/profiles */
        fields?: object[];
    };
    type ValuateFinanceConfiguration = {
        showValuateInquiry?: boolean;
        showResidualPrediction?: boolean;
        showPurchasingValue?: boolean;
        hideMargin?: boolean;
        hideRepairCost?: boolean;
        hideRoundedValues?: boolean;
        hideLumpSumValuation?: boolean;
        /** remove RepairCost from payload requests (set zero) */
        removeRepairCost?: boolean;
        defaultNoVARate?: number;
        /** for preset by default TaxationType in valuate */
        isDifferenceTaxationType?: boolean;
        /** for preset by default Gross in valuate */
        displayGross?: boolean;
        showPluginEquipment?: boolean;
        minAttachments?: number;
        damageDateAsDefaultForDeterminatedDate?: boolean;
        freeValuationEnabled?: boolean;
        /** if true, mileage is optional in valuate */
        isMileageOptional?: boolean;
        valuateService?: 'FinanceLine' | 'valuateNG' | 'ValuationService';
        repairCostLevel?: {
            red: { min: number; max?: number };
            orange: { min: number; max?: number };
        };
    };
    type PartSelectionConfiguration = {
        /** defined in @shared-models/part-selection */
        advancedPartSelection?: object[];
        hideMyClaimParts?: boolean;
    };

    type EquipmentConfiguration = {
        settings: {
            /** if true you can add manual position equipment (only APPRAISAL) */
            showManualPositions?: boolean;
            /** if true for you available work with calculation and valuation equipment in one restriction */
            isAdvancedEquipment?: boolean;
            /** need for hide equipment in printout */
            isShowButtonHiddenEquipment?: boolean;
            /** if true show equipment graphics in table (true by default) */
            showGraphics?: boolean;
            /** if true show switcher for filtering rims in equipment table */
            showFilterByRims?: boolean;
        };
    };

    enum AcceptAttribute {
        Images = 'image/*',
        Audio = 'audio/*',
        Video = 'video/*',
        PDF = 'application/pdf',
        Word = 'application/msword',
        Excel = 'application/vnd.ms-excel',
        Text = 'text/plain',
        HTML = 'text/html',
        CSV = 'text/csv',
        Zip = 'application/zip',
        JSON = 'application/json'
    }

    type AttachmentGroup = {
        /** Name of the folder */
        name: string;
        /** ID of the folder. Can be found in the URL when you enter the folder in the myClaim Administration. */
        id: number;
        /** Here you can define which type of files are accepted. Some examples : application/pdf, image/jpeg, image/*. See more here : https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes/accept. */
        accept?: AcceptAttribute | string;
        /** Maximum number of attachments. */
        max?: number;
        /** Set the visibility of attachments. */
        visible?: boolean;
        /** Set the ability to delete attachments. */
        deletable?: boolean;
        /** Makes attachments not editable. */
        readOnly?: boolean;
        /** Settings for the guided photo capture module */
        photoCapture?: PluginCarsConfiguration;
        withOverlayText?: boolean;
        overlayText?: string;
        tag?: number | string;
    };

    type GalleryConfiguration = {
        /** Folder ID of the folder */
        anonymizedImagesFolderId?: number;
        compressToSize?: number;
        sortImagesOrder?: 'asc' | 'desc';
        initialQuality?: number;
        groups?: AttachmentGroup[];
        /** Configuration for the "Take Photo" button */
        photoCapture?: PluginCarsConfiguration;
        /** Enable the ability to write a comment on an attachment ( Def : False )
         * @default false */
        showComment?: boolean;
        showEditor?: boolean;
        pdfFolderId?: any;
        removePhotoAfterConversion?: any;
        /** Photo editor inside gallery */
        fileEditor?: any; // TODO remove any
        /** Force the additional photo to be taken in landscape mode ( Def : False )
         * @default false */
        additionalPhotoForceLandscape?: any;
        /** Enable/Disable the ability to manually upload a photo ( Def : True )
         * @default true */
        allowManualUpload?: boolean;
        /**
         * Enables the user to enter a custom name for the resulting PDF
         * when converting one or more JPGs to PDF.
         * @default false
         */
        allowCustomPdfName?: boolean;
        compactView?: boolean;
        withoutHorizontalScroll?: boolean;
        withoutGoBackButton?: boolean;
    };

    interface AiModelsInput {
        checkerModel: string;
        keyPointModel: string;
    }

    type PluginCarsConfiguration = {
        /** ai model version */
        aiModels?: AiModelsInput;
        /** content for validation modal */
        validationContent?: DAT2.Plugins.PluginCars.ValidationContent;
        /** to load test data for debugging in dev stand */
        debug?: boolean;
        /** for turn on real type validation with new model and enable animations */
        realTimeDetection?: boolean;
        /** for turn on real type validation with ai */
        realTime?: boolean;
        gpsMandatory?: boolean;
        embedGeo?: boolean;
        steps?: DAT2.Plugins.PluginCars.PluginStep[];
        layout?: DAT2.Plugins.PluginCars.Layout;
        additionalPhotoLayout?: DAT2.Plugins.PluginCars.Layout;
        additionalPhotoForceLandscape?: boolean;
        additionalPhotoFolderId?: number;
        additionalStepConfirm?: boolean;
        modernView?: boolean;
        showPrivacyPolicyStep?: boolean;
        showGalleryButton?: boolean;
        showSettingsButton?: boolean;
        downloadOnDevice?: boolean;
        showConfirmationStep?: boolean;
        hideStartAgainButton?: boolean;
        gpsInfo?: DAT2.Plugins.PluginCars.GpsInfo;
        privacyPolicyLink?: string;
        /** autocapture image if AI validation recognized it */
        autocapture?: boolean;
        /** disable button if AI is running and photo not yet recognized */
        aiDisabled?: boolean;
        /** define car type to determine ai validation */
        carType?: DAT2.Plugins.PluginCars.CarType;
        /** for dynamic mask SVG definition */
        datECode?: string;
        /** show summary page
         *  if false completeFormCallback invoke in last capture photo step
         *  @default true */
        withSummary?: boolean;
        withGoBackButton?: boolean;
        /** @default true */
        disableStreamAfterComplete?: boolean;
        /** for FailedScreen on ConfirmationStep */
        allowRetakeFailedSteps?: boolean;
        allowResendFailedSteps?: boolean;
        allowSkipFailedStep?: boolean;
        /** trigger automatic search for identify vehicle */
        searchAutomatic?: boolean;
        onCompleteAiStepsResult?: (result: AiPhotosJson) => void;
        translates?: Record<string, any>;
        /** images zip file name  */
        licensePlate?: string;
        clearPhotoOnOpen?: boolean;
        additionalPhotoTags?: number[];
        /** Possibility to compress image */
        isCompressImage?: boolean;
        compressToSize?: number;
    };

    type MailConfiguration = {
        mailboxFolderId?: number;
    };

    type AiClaimConfiguration = {
        sync?: boolean;
        showModalSuccess?: boolean;
        isShownDamageSelector?: boolean;
        isDamageSelectorOptional?: boolean;
        photoCapture?: PluginCarsConfiguration;
        /** Enable/Disable the ability to manually upload a photo ( Def : True )
         * @default true */
        allowManualUpload?: boolean;
        folderId?: number;
        labourRatesStep?: boolean;
        useOrchestrator?: boolean;
        tag?: number | string;
        isOpen?: boolean;
        hideUploadFromDevice?: boolean;
    };

    type AiGalleryConfiguration = {
        hasPanel?: boolean;
        hasPanelDamage?: boolean;
        isShownFixedTooltip?: boolean;
        AttachmentFolderId?: number | undefined;
        DamageColors?: DAT2.Plugins.AiGallery.DamageColors;
        PanelColors?: DAT2.Plugins.AiGallery.PanelColors;
        slidesPerView?: number | 'auto';
        isReadOnly?: boolean;
    };

    type ClaimStepperConfiguration = {
        isShownStepperHeading?: boolean;
        hideFooter?: boolean;
        redirectTo?: string;
        config?: DAT2.Plugins.ClaimStepper.Step[] | null;
    };

    type PrintoutConfiguration = {
        printoutReports?: Plugins.Printout.PrintoutReport[];
        isDefaultReportShown?: boolean;
        defaultPrintoutDescription?: string;
        defaultUpdateProfileInfo?: boolean;
    };

    type InboxConfiguration = {
        /** don't do it like with dynamic_columns, it should be inside the setting */
        dynamic_columns?: DAT2.Plugins.Inbox.DynamicColumns;
        kanban_board?: DAT2.Plugins.Inbox.KanbanBoard;
        statusSteps?: string[][];
        settings?: {
            isKanbanBoardShown?: boolean;
            actionMenuType?: 'row' | 'column';
            /** for special bid claims list view */
            isBidViewEnabled?: boolean;
            /** used to assign collaborator right after user assignment */
            collaboratorAssignmentTemplateIds?: number[];
            insuranceCode?: string;
            displayGross?: boolean;
            isShownTimeForDateFormat?: boolean;
            pageLimit?: string;
            carValue?: CarValue;
            /** "importIni": "importIni" */
            importIni?: string;
            importIniFolder?: number;
            isVisibleProductivityTab?: boolean;
            isVisibleHistoryOfInspectionsTab?: boolean;
            isVisibleChatLogTab?: boolean;
            efficiency?: {
                enabled?: boolean;
                gaugeDisabled?: boolean;
                /** Example: 4 */
                expireYellowFlag?: number;
            };
            productivityProfiles?: DAT2.Plugins.Inbox.ProductivityProfile[];
            historyOfInspection?: DAT2.Plugins.Inbox.HistoryOfInspection;
            columns?: {
                isShownExpert?: boolean;
                isShownRepairer?: boolean;
                hidden?: DAT2.Plugins.Inbox.TableBlocks[];
            };
            svStatusColorsMap?: Record<'X' | 'I' | 'T' | 'G' | 'F', string>;
            euroSvColumns?: DAT2.Plugins.Inbox.EuroSvColumns;
            activityColumns?: DAT2.Plugins.Inbox.ActivityColumns;
            preview?: {
                groupId: number;
                generalSection?: DAT2.Plugins.Inbox.PreviewGroup[];
                commentSection?: {
                    /** notes */
                    id?: string;
                    readOnly?: boolean;
                    fieldName?: string;
                };
                hideTabs?: DAT2.Plugins.Inbox.PreviewTabs[];
            };
            formatDateForImportEuroSv?: string;
            isEnabledEuroSvMultipleSelection?: boolean;
            isEnabledEuroSvShowProgressImports?: boolean;
            columnsShownBasedOnFilter?: DAT2.Plugins.Inbox.ColumnsShownBasedOnFilter;
            defaultSelectedSearchOption?: DAT2.TableBlocks;
            additionalColumnsForInbox?: DAT2.Plugins.Inbox.TableColumn[];
            showSvdImport?: boolean;
            euroSvMultipleExport?: {
                isEnabled?: boolean;
                downloadZips?: boolean;
                statuses?: {
                    close?: string;
                    failedZip?: string;
                };
            };
            replaceCarOwnerto?: string;
            isDefaultColumnsHiddenInSearch?: boolean;
            folderFilterOrder?: string[];
            styledColumns?: {
                [key: string]: {
                    isWithoutIcon?: boolean;
                    isWithoutText?: boolean;
                    [category: string]: string[];
                };
            };
            mobileColumnsVisibility?: {
                isShownStatusNameOnBottom?: boolean;
            };
            isVisibleDeductible?: boolean;
            isVisibleReimbursement?: boolean;
            numberOfDaysForDeductible?: number;
            showRowChatNotifications?: boolean;
        };
        subjects?: {
            showList: boolean;
            readOnly: boolean;
            label: string;
            icon: string;
            Type: string;
            id: string;
        }[];
    };

    type GrapaConfiguration = {
        /** if true show settings button "gear wheel" */
        debuggingMode?: boolean;
        locale?: {
            /** Example: 'ru' */
            country: string;
            /** parameter datCountryIndicator will be ignored and used value from contract.Dossier.Country (like 'ru') */
            datCountryIndicator: string;
            /** Example: RU */
            language: string;
        };
        useSmartRepair?: boolean;
        useHailDamages?: boolean;
        useHailSmartRepair?: boolean;
        hideCommonRepair?: boolean;
        priceFormat?: {
            locales?: string;
            numberFormatOptions?: Intl.NumberFormatOptions;
        };
        isReadOnly?: boolean;
        showPriceHistory?: boolean;
        showSparePartPrice?: boolean;
        showDiscountIndicator?: boolean;
        isSparePartPriceEditable?: boolean;
        isSparePartNumberEditable?: boolean;
        useDATTheme?: boolean;
        maxWidth?: string;
        height?: string;
        maxMobileWidth?: number;
        showFullscreen?: boolean;
        pinchAndWheelZoom?: boolean;
        // graphicDamageFormId?: string; // todo
        isUsePartCompletionService?: boolean;
        isPreviewGraphicEnable?: boolean;
        showFastMovingBar?: boolean;
        showMenuGenericGroups?: boolean;
        showMenuGenericGraphics?: boolean;
        showZoneGraphicPreview?: boolean;
        useReplaceMultiSelect?: boolean;
        showFastSelectDamageForm?: boolean;
        showZoomButton?: boolean;
        isShowAdditionalRepairProcess?: boolean;
        showManualPositionButton?: boolean;
        showCommentButton?: boolean;
        showOnlyGraphicGroupByFastTrackDATID?: boolean;
        /** true completely hide it for "onlyGlass" product */
        showLeftDrawer?: boolean;
        /** true by default open or close by default */
        showLeftPanel?: boolean;
        zoneFilter?: {
            // showZoneId?: number[]; // white list of zone id to show todo maybe if require
            /** black list of zone id to hide */
            hideZoneId?: number[];
        };
        showSVGIconsRepairPositions?: boolean /** decrease performance for big number of repair positions */;
        useFastTrackDamagesOnGenericGraphic?: boolean;
        useInterimCalculation?: boolean;
        showIntermediateCalculationSummary?: boolean;
        showIntermediateTotalPriceOnly?: boolean;
        showLastCalculationResult?: boolean;
        autoCalculation?: boolean;
        calculateOnConfirm?: boolean;
        skipEquipmentPageIfVIN?: boolean;
        showIncludedPositionsInfo?: boolean;

        defaultRepairWorkTime?: number;
        defaultTimeUnit?: string;
        partsImagesFolderId?: number;
        galleryConfiguration?: GalleryConfiguration;
        useAdditionalPrice?: boolean;
        useWorkTypeLevel?: boolean;
        useServiceForWorkTypeWorkLevel?: boolean;
        useTurkishMaintenance?: boolean;
        enableHailDamage?: boolean;
        enableCavityUnderBodyProtection?: boolean;
        enableIFLPositions?: boolean;
        /** default DamageIds  'EReplace' */
        defaultDamageIds?: string[];
        /** white list for repair codes */
        enableRC?: string[];
        /** disabled repair codes */
        disableRC?: string[];
        /** for "onlyReplace" product */
        replaceWithoutTime?: boolean;
        /** ['U', 'M', 'P', 'Z', 'S', 'T'] */
        additionalRC?: string[]; // TODO
        /** all available repair codes for manual position  */
        availableRCForManualPosition?: string[];
        useRepairExtension?: boolean;
        plasticRepair?: DAT2.Plugins.Grapa.PlasticRepair;
        glassRepair?: DAT2.Plugins.Grapa.GlassRepair[];

        lacquerLevelByDefault?: string; // al except replace 'new' | 'minor' | 'major' | 'surface' | 'none'
        fixedLacquerLevel?: string; // 'new' | 'minor' | 'major' | 'surface' | 'none'
        customReplaceDefaultLacquer?: DAT2.Plugins.Grapa.ReplaceDefaultLacquer[]; // seems like not used
        hideLacquerNone?: boolean;
        disableLacquerNewIfReplace?: boolean;
        useRepairCodeMForLacquerNew?: boolean;

        groupsByFastTrackDATID?: DAT2.Plugins.Grapa.MenuGroupsByFastTrackDATIDType[];
        genericSVGGraphics?: DAT2.Plugins.Grapa.GenericSVGGraphic[];
        genericSVGGraphicsByKindOfSVG?: Array<{
            kindOfSVG: string;
            genericSVGGraphics?: DAT2.Plugins.Grapa.GenericSVGGraphic[];
        }>;
        fastMovingBar?: DAT2.Plugins.Grapa.FastMovingBarItems[];

        // ftSettings?: {
        //     ftDamages?: ftDamagesType;
        //     ftRepairs?: ftRepairType[];
        //     ftGroups?: ftGroupsType;
        //     ftDvnMap?: FtDvnMapType;
        // };

        i18n?: {
            resources?: Resource;
        };
        showFastReplace?: boolean;
        defaultRepairPositionWorkLevel?: 1 | 2 | 3;

        hiddenToolbarButtons?: DAT2.Plugins.Grapa.HiddenToolbarButton[];
        repairViewFastFields?: {
            replace?: DAT2.Plugins.Grapa.CommonRepairViewField[];
            overhaulFixing?: DAT2.Plugins.Grapa.CommonRepairViewField[];
            disAndMounting?: DAT2.Plugins.Grapa.CommonRepairViewField[];
            lacquer?: DAT2.Plugins.Grapa.CommonRepairViewField[];
            spotRepair?: DAT2.Plugins.Grapa.CommonRepairViewField[];
        };

        cesviEnabled?: boolean;
        compressToSize?: number;

        hiddenEquipmentIds?: number[];
    };

    type LabourRatesGenericConfiguration = {
        fieldsDictionary?: DAT2.Plugins.LabourRatesGeneric.LabourRateField[];
        allLabourRatesMandatory?: boolean;
    };

    type VhcConfiguration = {
        vhcConfig: DAT2.Plugins.VHC.VHCConfigType;
    };

    type FastTrackConfiguration = {
        settings?: {
            debuggingMode?: boolean /** if true show settings button "gear wheel" */;

            locale?: {
                country: string /** 'ru' */;
                datCountryIndicator: string /** 'ru' */;
                language: string /** 'RU' */;
            };

            fairGarageVersion?: boolean;
            threeDmode?: boolean;
            maxWidth?: string;
            maxWidthSVG?: string /** max width of SVG picture */;
            maxMobileWidth?: number;
            backgroundColor?: string;

            showSummaryPanel?: boolean;
            showSummaryPanelFooter?: boolean;

            // saveFTDamageOnChange?: boolean; // auto save fast track damage on every change

            /** show or not block with fast-track element select menu */
            showFastTrackElementsMenu?: boolean;
            fastTrackElementsMenuModal?: boolean;
            fastTrackElementsMenu?: DAT2.Plugins.FastTrack.FastTrackElementsMenuGroup[];

            /** fairGarageVersion */
            costRates?: DAT2.Plugins.FastTrack.CostRates;
            fastTrackServiceElements?: DAT2.Plugins.FastTrack.FastTrackElementsGroup[];
            fastTrackRepairElements?: DAT2.Plugins.FastTrack.FastTrackElementsGroup[];

            showSVGFastTrack?: boolean;
            fastTrackViews?: DAT2.Plugins.FastTrack.FastTrackViews;

            genericSVGGraphics?: DAT2.Plugins.FastTrack.GenericSVGGraphic[];

            customVehicleTypeSVG?: DAT2.Plugins.FastTrack.CustomVehicleTypeSVG[];
            customDatECodeSVG?: DAT2.Plugins.FastTrack.CustomDatECodeSVG[];

            showSparePartsCosts?: boolean;
            showListDamages?: boolean;
            showAiOrManualIcons?: boolean;
            showOnlyAiDamages?: boolean;
            showFraudDetection?: boolean;
            allowAddManualPosition?: boolean;
            compressToSize?: number;
            damageCatalogues?: DAT2.Plugins.FastTrack.DamageCatalogue[];
            hiddenDamages?: DAT2.Plugins.FastTrack.HiddenDamage[];

            orchestrator?: DAT2.Plugins.FastTrack.Orchestrator;

            partsImagesFolderId?: number;

            rentalCar?: DAT2.Plugins.FastTrack.RentalCar;

            groupMenuColSpan?: number;
            groupMenuColSpanMobile?: number;
            // TODO: any
            // predefinedVehicleSelectionBase?: any[];

            autoCalculation?: boolean;
            showResultTable?: boolean; // ???
            resultFormat?: {
                locales?: string;
                numberFormatOptions?: Intl.NumberFormatOptions;
            };

            // fastTrackConfiguration?: FastTrackConfiguration;
            i18n?: {
                resources: Resource;
            };
            damageColors?: DAT2.Plugins.FastTrack.DamageColors;
        };
    };

    type DarvaConfiguration = {
        invoiceStatus: {
            name: string;
            id: number;
        };
        errorStatus: {
            name: string;
            id: number;
        };
    };

    type AssignPartnerConfiguration = {
        settings: {
            showAllAvailableRoles: boolean;
            hideRoles?: DAT2.CustomerRole[];
        };
    };

    //for Kanban-board
    interface ColumnCard {
        id: string;
        title: string;
        initialValue: string;
        selectOptions: SelectOption[];
        contract: DAT2.Internal.ClaimItem;
        handleContractClick: (e: React.MouseEvent, contract: DAT2.Internal.ClaimItem) => void;
    }

    interface Column {
        id: string;
        title: string;
        cards: ColumnCard[];
    }

    type KanbanBoardProps = {
        board: {
            columns: Column[];
        };
        components?: {
            Card: React.ComponentType<ColumnCard>;
        };
        handleDragEnd?: () => void;
    };

    type SpireContract = {
        isEnabledCreateContractButton?: boolean;
        isComponent?: boolean;
        contractItems?: Record<string, string>;
    };
    // Plugins end

    type KnownProductsConfiguration = {
        'ai-claim'?: AiClaimConfiguration;
        'ai-gallery'?: AiGalleryConfiguration;
        calculation?: CalculationConfiguration;
        'claim-management'?: ClaimManagementConfiguration;
        'claim-stepper'?: ClaimStepperConfiguration;
        'labour-rates-generic'?: LabourRatesGenericConfiguration;
        'assign-partner'?: AssignPartnerConfiguration;
        vhc?: VhcConfiguration;
        darva?: DarvaConfiguration;
        equipment?: EquipmentConfiguration;
        'fast-track'?: FastTrackConfiguration;
        gallery?: GalleryConfiguration;
        grapa?: GrapaConfiguration;
        inbox?: InboxConfiguration;
        'italian-calculation'?: ItalianCalculationConfiguration;
        'labour-rates'?: LabourRatesConfiguration;
        mail?: MailConfiguration;
        'part-selection'?: PartSelectionConfiguration;
        printout?: PrintoutConfiguration;
        profile?: ProfileConfiguration;
        'valuate-finance'?: ValuateFinanceConfiguration;
        'vehicle-selection'?: VehicleSelectionConfiguration;
        'plugin-cars'?: PluginCarsConfiguration;
        'spire-contract'?: SpireContract;
    };

    type ProductsConfiguration = UnknownProductsConfiguration & KnownProductsConfiguration;

    /* Claim builder */
    type ClaimBuilder = {
        /** Enter the name of the claim */
        name: string;
        /** Enter the id of the template.\nCan be found in myClaim->Administration->Manage Templates->EnterYourTemplate and then at the end of the url there is a number. */
        templateId: TemplateId;
        /** Enter one of the options:
         *  "standard" is the standard flow of claim.
         *  "AI" is the custom flow for AI.
         *  "guided" is the claim-stepper flow. */
        layoutType: LayoutType;
        /** for show importIniInput on TemplateCard. Import launches internal logic of weDat for parse ini and creates new contract using current template **/
        showImportIni?: boolean;
        templateIconUrl?: string;
    };

    type LayoutType = 'standard' | 'AI' | 'guided' | 'importIniAlternative';

    type YouCitCredentials = {
        username: string;
        password: string;
    };

    type EuroSvCredentials = {
        username: string;
        password: string;
    };

    type BasicValuationConfItem = {
        residualValue: number;
        purchasePrice: number;
        greater: 'residualValue' | 'purchasePrice';
        isEqual?: boolean;
    };
}
